import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:parlant_flutter/app/app.locator.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/journey.dart';
import 'package:parlant_flutter/features/journey_builder/domain/repositories/journey_repository.dart';
import 'package:parlant_flutter/features/journey_builder/presentation/view_models/journey_canvas_view_model.dart';

class MockJourneyRepository extends Mock implements JourneyRepository {}

void main() {
  group('JourneyCanvasViewModel Tests -', () {
    late JourneyRepository mockJourneyRepository;

    setUp(() {
      locator.reset();
      mockJourneyRepository = MockJourneyRepository();
      locator.registerSingleton<JourneyRepository>(mockJourneyRepository);
      registerFallbackValue(
        const Journey(id: 'any', nodes: [], connections: []),
      );
    });

    tearDown(locator.reset);

    test(
      '''
When viewModel is initialised and no journey exists, 
it should create a new one
''',
      () async {
        when(
          () => mockJourneyRepository.getJourney(),
        ).thenAnswer((_) async => null);
        when(
          () => mockJourneyRepository.saveJourney(any()),
        ).thenAnswer((_) async {});

        final model = JourneyCanvasViewModel();
        await model.initialise();

        expect(model.journey, isNotNull);
        expect(model.journey!.nodes, isEmpty);
        expect(model.journey!.connections, isEmpty);
        verify(() => mockJourneyRepository.getJourney()).called(1);
        verify(() => mockJourneyRepository.saveJourney(any())).called(1);
      },
    );

    test(
      'When viewModel is initialised and a journey exists, it should load it',
      () async {
        const existingJourney = Journey(id: '1', nodes: [], connections: []);
        when(
          () => mockJourneyRepository.getJourney(),
        ).thenAnswer((_) async => existingJourney);

        final model = JourneyCanvasViewModel();
        await model.initialise();

        expect(model.journey, existingJourney);
        verify(() => mockJourneyRepository.getJourney()).called(1);
        verifyNever(() => mockJourneyRepository.saveJourney(any()));
      },
    );

    // test(
    //   'When addNode is called, it should add a node to the journey and save
    //   it',
    //   () async {
    //     const initialJourney = Journey(id: '1', nodes: [], connections: []);
    //     when(
    //       () => mockJourneyRepository.getJourney(),
    //     ).thenAnswer((_) async => initialJourney);
    //     when(
    //       () => mockJourneyRepository.saveJourney(any()),
    //     ).thenAnswer((_) async {});

    //     final model = JourneyCanvasViewModel();
    //     await model.initialise();

    //     model.addNode(NodeType.start, const Offset(10, 20));

    //     expect(model.journey!.nodes.length, 1);
    //     expect(model.journey!.nodes.first.type, NodeType.start);
    //     expect(model.journey!.nodes.first.position, const Offset(10, 20));
    //     verify(() => mockJourneyRepository.saveJourney(any())).called(1);
    //   },
    // );

    // test(
    //   'When updateNodePosition is called, it should update the node position
    //   and save',
    //   () async {
    //     const node = models.Node(
    //       id: 'node1',
    //       type: NodeType.start,
    //       position: Offset(0, 0),
    //     );
    //     const initialJourney = Journey(id: '1', nodes: [node],
    //       connections: [],);
    //     when(
    //       () => mockJourneyRepository.getJourney(),
    //     ).thenAnswer((_) async => initialJourney);
    //     when(
    //       () => mockJourneyRepository.saveJourney(any()),
    //     ).thenAnswer((_) async {});

    //     final model = JourneyCanvasViewModel();
    //     await model.initialise();

    //     model.updateNodePosition('node1', const Offset(100, 150));

    //     expect(model.journey!.nodes.first.position, const Offset(100, 150));
    //     verify(() => mockJourneyRepository.saveJourney(any())).called(1);
    //   },
    // );

    // test(
    //   'When addConnection is called, it should add a connection and save',
    //   () async {
    //     const initialJourney = Journey(id: '1', nodes: [], connections: []);
    //     when(
    //       () => mockJourneyRepository.getJourney(),
    //     ).thenAnswer((_) async => initialJourney);
    //     when(
    //       () => mockJourneyRepository.saveJourney(any()),
    //     ).thenAnswer((_) async {});

    //     final model = JourneyCanvasViewModel();
    //     await model.initialise();

    //     model.addConnection('node1', 'node2', 'out', 'in');

    //     expect(model.journey!.connections.length, 1);
    //     expect(model.journey!.connections.first.fromNodeId, 'node1');
    //     expect(model.journey!.connections.first.toNodeId, 'node2');
    //     verify(() => mockJourneyRepository.saveJourney(any())).called(1);
    //   },
    // );
  });
}
