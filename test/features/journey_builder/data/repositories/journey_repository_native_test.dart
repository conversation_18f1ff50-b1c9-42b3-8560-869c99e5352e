import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:parlant_flutter/features/journey_builder/data/repositories/journey_repository_native.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/connection.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/journey.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/node.dart';
import 'package:surrealdb/surrealdb.dart';

class MockSurrealDB extends Mock implements SurrealDB {}

void main() {
  group('JourneyRepositoryImpl (Native)', () {
    late JourneyRepositoryImpl journeyRepository;
    late MockSurrealDB mockSurrealDB;

    setUp(() {
      mockSurrealDB = MockSurrealDB();
      journeyRepository = JourneyRepositoryImpl(db: mockSurrealDB);
    });

    test(
      'getJourney should return a journey when the database is not empty',
      () async {
        when(() => mockSurrealDB.connect()).thenReturn(null);
        when(() => mockSurrealDB.wait()).thenAnswer((_) async {});
        when(() => mockSurrealDB.use('test', 'test')).thenAnswer((_) async {});
        when(
          () => mockSurrealDB.select<Map<String, dynamic>>('journey'),
        ).thenAnswer(
          (_) async => [
            {
              'id': '1',
              'nodes': <Node>[],
              'connections': <Connection>[],
            },
          ],
        );

        final journey = await journeyRepository.getJourney();

        expect(journey, isA<Journey>());
        expect(journey!.id, '1');
      },
    );

    test('getJourney should return null when the database is empty', () async {
      when(() => mockSurrealDB.connect()).thenReturn(null);
      when(() => mockSurrealDB.wait()).thenAnswer((_) async {});
      when(() => mockSurrealDB.use('test', 'test')).thenAnswer((_) async {});
      when(
        () => mockSurrealDB.select<Map<String, dynamic>>('journey'),
      ).thenAnswer((_) async => []);

      final journey = await journeyRepository.getJourney();

      expect(journey, isNull);
    });

    test('saveJourney should call the update method on the database', () async {
      when(() => mockSurrealDB.connect()).thenReturn(null);
      when(() => mockSurrealDB.wait()).thenAnswer((_) async {});
      when(() => mockSurrealDB.use('test', 'test')).thenAnswer((_) async {});
      when(() => mockSurrealDB.update(any(), any())).thenAnswer((_) async {
        return null;
      });

      await journeyRepository.saveJourney(
        const Journey(id: '1', nodes: [], connections: []),
      );

      verify(() => mockSurrealDB.update('journey', any())).called(1);
    });
  });
}
