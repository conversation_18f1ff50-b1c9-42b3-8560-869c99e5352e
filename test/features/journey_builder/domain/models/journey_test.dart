import 'package:flutter_test/flutter_test.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/connection.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/journey.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/node.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/node_type.dart';

void main() {
  group('Journey', () {
    test('from<PERSON>son and to<PERSON>son should work correctly', () {
      const journey = Journey(
        id: '1',
        nodes: [
          Node(
            id: '1',
            type: NodeType.start,
            position: Offset.zero,
          ),
        ],
        connections: [
          Connection(
            id: '1',
            fromNodeId: '1',
            toNodeId: '2',
            fromPortId: 'out',
            toPortId: 'in',
          ),
        ],
      );
      final json = journey.toJson();
      final newJourney = Journey.fromJson(json);
      expect(newJourney, journey);
    });

    test('copyWith should work correctly', () {
      const journey = Journey(
        id: '1',
        nodes: [
          Node(
            id: '1',
            type: NodeType.start,
            position: Offset.zero,
          ),
        ],
        connections: [
          Connection(
            id: '1',
            fromNodeId: '1',
            toNodeId: '2',
            fromPortId: 'out',
            toPortId: 'in',
          ),
        ],
      );
      final newJourney = journey.copyWith(id: '2');
      expect(newJourney.id, '2');
      expect(newJourney.nodes, journey.nodes);
      expect(newJourney.connections, journey.connections);
    });
  });
}
