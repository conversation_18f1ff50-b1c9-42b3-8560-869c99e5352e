import 'package:flutter_test/flutter_test.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/action.dart';

void main() {
  group('Action', () {
    test('fromJson and toJson should work correctly', () {
      const action = Action(
        type: 'test',
        value: 'test',
      );
      final json = action.toJson();
      final newAction = Action.fromJson(json);
      expect(newAction, action);
    });

    test('copyWith should work correctly', () {
      const action = Action(
        type: 'test',
        value: 'test',
      );
      final newAction = action.copyWith(type: 'new_test');
      expect(newAction.type, 'new_test');
      expect(newAction.value, 'test');
    });
  });
}
