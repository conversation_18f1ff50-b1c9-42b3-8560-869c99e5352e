import 'package:flutter_test/flutter_test.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/condition.dart';

void main() {
  group('Condition', () {
    test('from<PERSON><PERSON> and to<PERSON>son should work correctly', () {
      const condition = Condition(
        fact: 'test',
        operator: 'equal',
        value: 'test',
      );
      final json = condition.toJson();
      final newCondition = Condition.fromJson(json);
      expect(newCondition, condition);
    });

    test('copyWith should work correctly', () {
      const condition = Condition(
        fact: 'test',
        operator: 'equal',
        value: 'test',
      );
      final newCondition = condition.copyWith(fact: 'new_test');
      expect(newCondition.fact, 'new_test');
      expect(newCondition.operator, 'equal');
      expect(newCondition.value, 'test');
    });
  });
}
