import 'package:flutter_test/flutter_test.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/action.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/condition.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/guideline.dart';

void main() {
  group('Guideline', () {
    test('from<PERSON><PERSON> and to<PERSON>son should work correctly', () {
      const guideline = Guideline(
        conditions: [
          Condition(
            fact: 'test',
            operator: 'equal',
            value: 'test',
          ),
        ],
        action: Action(
          type: 'test',
          value: 'test',
        ),
      );
      final json = guideline.toJson();
      final newGuideline = Guideline.fromJson(json);
      expect(newGuideline, guideline);
    });

    test('copyWith should work correctly', () {
      const guideline = Guideline(
        conditions: [
          Condition(
            fact: 'test',
            operator: 'equal',
            value: 'test',
          ),
        ],
        action: Action(
          type: 'test',
          value: 'test',
        ),
      );
      final newGuideline = guideline.copyWith(
        action: const Action(
          type: 'new_test',
          value: 'test',
        ),
      );
      expect(newGuideline.action.type, 'new_test');
      expect(newGuideline.conditions, guideline.conditions);
    });
  });
}
