import 'package:flutter_test/flutter_test.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/node.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/node_type.dart';

void main() {
  group('Node', () {
    test('from<PERSON><PERSON> and to<PERSON><PERSON> should work correctly', () {
      const node = Node(
        id: '1',
        type: NodeType.start,
        position: Offset.zero,
      );
      final json = node.toJson();
      final newNode = Node.fromJson(json);
      expect(newNode, node);
    });

    test('copyWith should work correctly', () {
      const node = Node(
        id: '1',
        type: NodeType.start,
        position: Offset.zero,
      );
      final newNode = node.copyWith(id: '2');
      expect(newNode.id, '2');
      expect(newNode.type, NodeType.start);
      expect(newNode.position, Offset.zero);
    });
  });
}
