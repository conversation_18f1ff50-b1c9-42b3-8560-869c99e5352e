import 'package:flutter_test/flutter_test.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/connection.dart';

void main() {
  group('Connection', () {
    test('from<PERSON>son and to<PERSON>son should work correctly', () {
      const connection = Connection(
        id: '1',
        fromNodeId: '1',
        toNodeId: '2',
        fromPortId: 'out',
        toPortId: 'in',
      );
      final json = connection.toJson();
      final newConnection = Connection.fromJson(json);
      expect(newConnection, connection);
    });

    test('copyWith should work correctly', () {
      const connection = Connection(
        id: '1',
        fromNodeId: '1',
        toNodeId: '2',
        fromPortId: 'out',
        toPortId: 'in',
      );
      final newConnection = connection.copyWith(id: '2');
      expect(newConnection.id, '2');
      expect(newConnection.fromNodeId, '1');
      expect(newConnection.toNodeId, '2');
      expect(newConnection.fromPortId, 'out');
      expect(newConnection.toPortId, 'in');
    });
  });
}
