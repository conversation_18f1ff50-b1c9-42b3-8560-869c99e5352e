import 'package:flutter_test/flutter_test.dart';
import 'package:parlant_flutter/features/journey_builder/domain/services/placeholder_engine_service.dart';

void main() {
  group('PlaceholderEngineServiceTest -', () {
    test('When processValue is called, it should append "-processed"', () {
      final service = PlaceholderEngineService();
      final result = service.processValue('test');
      expect(result, 'test-processed');
    });
  });
}
