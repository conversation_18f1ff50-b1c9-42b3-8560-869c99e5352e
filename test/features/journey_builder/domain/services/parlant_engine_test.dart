import 'package:flutter_test/flutter_test.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/action.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/condition.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/guideline.dart';
import 'package:parlant_flutter/features/journey_builder/domain/services/parlant_engine.dart';

void main() {
  late ParlantEngine parlantEngine;

  setUp(() {
    parlantEngine = ParlantEngine();
  });

  group('ParlantEngine', () {
    test(
      'should return the correct action when a guideline condition is met',
      () {
        final guidelines = [
          const Guideline(
            conditions: [
              Condition(fact: 'age', operator: 'equal', value: 30),
            ],
            action: Action(type: 'show_message', value: 'Welcome'),
          ),
        ];
        final input = {'age': 30};

        final result = parlantEngine.getActionForInput(input, guidelines);

        expect(result, isNotNull);
        expect(result!.type, 'show_message');
        expect(result.value, 'Welcome');
      },
    );

    test('should return null when no guideline condition is met', () {
      final guidelines = [
        const Guideline(
          conditions: [
            Condition(fact: 'age', operator: 'equal', value: 30),
          ],
          action: Action(type: 'show_message', value: 'Welcome'),
        ),
      ];
      final input = {'age': 25};

      final result = parlantEngine.getActionForInput(input, guidelines);

      expect(result, isNull);
    });

    test('should return the action from the first matching guideline', () {
      final guidelines = [
        const Guideline(
          conditions: [
            Condition(fact: 'age', operator: 'equal', value: 25),
          ],
          action: Action(type: 'show_message', value: 'Too young'),
        ),
        const Guideline(
          conditions: [
            Condition(fact: 'age', operator: 'equal', value: 30),
          ],
          action: Action(type: 'show_message', value: 'Welcome'),
        ),
      ];
      final input = {'age': 30};

      final result = parlantEngine.getActionForInput(input, guidelines);

      expect(result, isNotNull);
      expect(result!.type, 'show_message');
      expect(result.value, 'Welcome');
    });

    test('should correctly evaluate "notEqual" operator', () {
      final guidelines = [
        const Guideline(
          conditions: [
            Condition(fact: 'age', operator: 'notEqual', value: 25),
          ],
          action: Action(type: 'show_message', value: 'Not 25'),
        ),
      ];
      final input = {'age': 30};

      final result = parlantEngine.getActionForInput(input, guidelines);

      expect(result, isNotNull);
      expect(result!.value, 'Not 25');
    });

    test('should correctly evaluate "in" operator', () {
      final guidelines = [
        const Guideline(
          conditions: [
            Condition(
              fact: 'country',
              operator: 'in',
              value: ['USA', 'Canada'],
            ),
          ],
          action: Action(type: 'show_message', value: 'North America'),
        ),
      ];
      final input = {'country': 'Canada'};

      final result = parlantEngine.getActionForInput(input, guidelines);

      expect(result, isNotNull);
      expect(result!.value, 'North America');
    });

    test('should correctly evaluate "notIn" operator', () {
      final guidelines = [
        const Guideline(
          conditions: [
            Condition(
              fact: 'country',
              operator: 'notIn',
              value: ['USA', 'Canada'],
            ),
          ],
          action: Action(type: 'show_message', value: 'Not North America'),
        ),
      ];
      final input = {'country': 'Mexico'};

      final result = parlantEngine.getActionForInput(input, guidelines);

      expect(result, isNotNull);
      expect(result!.value, 'Not North America');
    });

    test('should correctly evaluate "lessThan" operator', () {
      final guidelines = [
        const Guideline(
          conditions: [
            Condition(fact: 'age', operator: 'lessThan', value: 30),
          ],
          action: Action(type: 'show_message', value: 'Under 30'),
        ),
      ];
      final input = {'age': 29};

      final result = parlantEngine.getActionForInput(input, guidelines);

      expect(result, isNotNull);
      expect(result!.value, 'Under 30');
    });

    test('should correctly evaluate "greaterThan" operator', () {
      final guidelines = [
        const Guideline(
          conditions: [
            Condition(fact: 'age', operator: 'greaterThan', value: 30),
          ],
          action: Action(type: 'show_message', value: 'Over 30'),
        ),
      ];
      final input = {'age': 31};

      final result = parlantEngine.getActionForInput(input, guidelines);

      expect(result, isNotNull);
      expect(result!.value, 'Over 30');
    });

    test('should return null if any condition is not met', () {
      final guidelines = [
        const Guideline(
          conditions: [
            Condition(fact: 'age', operator: 'greaterThan', value: 30),
            Condition(fact: 'country', operator: 'equal', value: 'USA'),
          ],
          action: Action(
            type: 'show_message',
            value: 'Welcome, American over 30',
          ),
        ),
      ];
      final input = {'age': 31, 'country': 'Canada'};

      final result = parlantEngine.getActionForInput(input, guidelines);

      expect(result, isNull);
    });
  });
}
