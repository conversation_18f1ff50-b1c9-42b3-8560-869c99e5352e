import 'package:parlant_flutter/features/journey_builder/domain/models/journey.dart';
import 'package:parlant_flutter/features/journey_builder/domain/repositories/journey_repository.dart';
import 'package:surrealdb/surrealdb.dart';

/// An implementation of the [JourneyRepository] that uses a SurrealDB
/// database.
class JourneyRepositoryImpl implements JourneyRepository {
  /// Creates a new instance of the [JourneyRepositoryImpl].
  JourneyRepositoryImpl({SurrealDB? db}) : _db = db ?? SurrealDB('mem://');
  final SurrealDB _db;

  Future<void> _init() async {
    _db.connect();
    await _db.wait();
    await _db.use('test', 'test');
  }

  @override
  Future<Journey?> getJourney() async {
    await _init();
    final result = await _db.select<Map<String, dynamic>>('journey');
    if (result.isEmpty || result.first.isEmpty) {
      return null;
    }
    return Journey.fromJson(result.first);
  }

  @override
  Future<void> saveJ<PERSON>ney(Journey journey) async {
    await _init();
    await _db.update('journey', journey.toJson());
  }
}
