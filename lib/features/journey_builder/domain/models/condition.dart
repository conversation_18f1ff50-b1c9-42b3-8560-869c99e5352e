import 'package:freezed_annotation/freezed_annotation.dart';

part 'condition.freezed.dart';
part 'condition.g.dart';

/// Represents a condition to be evaluated in the journey.
@freezed
abstract class Condition with _$Condition {
  /// Creates a new instance of [Condition].
  const factory Condition({
    /// The fact to be evaluated.
    required String fact,

    /// The operator to use for evaluation.
    required String operator,

    /// The value to compare against.
    required dynamic value,
  }) = _Condition;

  /// Creates a new instance of [Condition] from a JSON object.
  factory Condition.fromJson(Map<String, dynamic> json) =>
      _$ConditionFromJson(json);
}
