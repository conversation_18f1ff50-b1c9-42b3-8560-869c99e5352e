import 'package:freezed_annotation/freezed_annotation.dart';

part 'connection.freezed.dart';
part 'connection.g.dart';

/// A connection between two nodes in a journey.
@freezed
abstract class Connection with _$Connection {
  /// Creates a new instance of the [Connection].
  const factory Connection({
    required String id,
    required String fromNodeId,
    required String toNodeId,
    required String fromPortId,
    required String toPortId,
  }) = _Connection;

  /// Creates a new instance of the [Connection] from a JSON object.
  factory Connection.fromJson(Map<String, dynamic> json) =>
      _$ConnectionFromJson(json);
}
