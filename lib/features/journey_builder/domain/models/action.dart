import 'package:freezed_annotation/freezed_annotation.dart';

part 'action.freezed.dart';
part 'action.g.dart';

/// Represents an action to be taken in the journey.
@freezed
abstract class Action with _$Action {
  /// Creates a new instance of [Action].
  const factory Action({
    /// The type of action.
    required String type,

    /// The value of the action.
    required String value,
  }) = _Action;

  /// Creates a new instance of [Action] from a JSON object.
  factory Action.fromJson(Map<String, dynamic> json) => _$ActionFromJson(json);
}
