// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'journey.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Journey _$JourneyFromJson(Map<String, dynamic> json) => _Journey(
  id: json['id'] as String,
  nodes: (json['nodes'] as List<dynamic>)
      .map((e) => Node.fromJson(e as Map<String, dynamic>))
      .toList(),
  connections: (json['connections'] as List<dynamic>)
      .map((e) => Connection.fromJson(e as Map<String, dynamic>))
      .toList(),
);

Map<String, dynamic> _$JourneyToJson(_Journey instance) => <String, dynamic>{
  'id': instance.id,
  'nodes': instance.nodes,
  'connections': instance.connections,
};
