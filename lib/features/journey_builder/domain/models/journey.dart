import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/connection.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/node.dart';

part 'journey.freezed.dart';
part 'journey.g.dart';

/// A journey, which is a collection of nodes and connections.
@freezed
abstract class Journey with _$Journey {
  /// Creates a new instance of the [Journey].
  const factory Journey({
    required String id,
    required List<Node> nodes,
    required List<Connection> connections,
  }) = _Journey;

  /// Creates a new instance of the [Journey] from a JSON object.
  factory Journey.fromJson(Map<String, dynamic> json) =>
      _$JourneyFrom<PERSON>son(json);
}
