import 'dart:ui';

import 'package:json_annotation/json_annotation.dart';

/// A [JsonConverter] that converts an [Offset] to and from a JSON object.
class OffsetJsonConverter
    implements JsonConverter<Offset, Map<String, dynamic>> {
  /// Creates a new instance of the [OffsetJsonConverter].
  const OffsetJsonConverter();

  @override
  Offset fromJson(Map<String, dynamic> json) {
    return Offset(json['dx'] as double, json['dy'] as double);
  }

  @override
  Map<String, dynamic> toJson(Offset object) {
    return {
      'dx': object.dx,
      'dy': object.dy,
    };
  }
}
