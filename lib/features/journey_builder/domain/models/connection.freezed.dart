// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'connection.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Connection {

 String get id; String get fromNodeId; String get toNodeId; String get fromPortId; String get toPortId;
/// Create a copy of Connection
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ConnectionCopyWith<Connection> get copyWith => _$ConnectionCopyWithImpl<Connection>(this as Connection, _$identity);

  /// Serializes this Connection to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Connection&&(identical(other.id, id) || other.id == id)&&(identical(other.fromNodeId, fromNodeId) || other.fromNodeId == fromNodeId)&&(identical(other.toNodeId, toNodeId) || other.toNodeId == toNodeId)&&(identical(other.fromPortId, fromPortId) || other.fromPortId == fromPortId)&&(identical(other.toPortId, toPortId) || other.toPortId == toPortId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,fromNodeId,toNodeId,fromPortId,toPortId);

@override
String toString() {
  return 'Connection(id: $id, fromNodeId: $fromNodeId, toNodeId: $toNodeId, fromPortId: $fromPortId, toPortId: $toPortId)';
}


}

/// @nodoc
abstract mixin class $ConnectionCopyWith<$Res>  {
  factory $ConnectionCopyWith(Connection value, $Res Function(Connection) _then) = _$ConnectionCopyWithImpl;
@useResult
$Res call({
 String id, String fromNodeId, String toNodeId, String fromPortId, String toPortId
});




}
/// @nodoc
class _$ConnectionCopyWithImpl<$Res>
    implements $ConnectionCopyWith<$Res> {
  _$ConnectionCopyWithImpl(this._self, this._then);

  final Connection _self;
  final $Res Function(Connection) _then;

/// Create a copy of Connection
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? fromNodeId = null,Object? toNodeId = null,Object? fromPortId = null,Object? toPortId = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,fromNodeId: null == fromNodeId ? _self.fromNodeId : fromNodeId // ignore: cast_nullable_to_non_nullable
as String,toNodeId: null == toNodeId ? _self.toNodeId : toNodeId // ignore: cast_nullable_to_non_nullable
as String,fromPortId: null == fromPortId ? _self.fromPortId : fromPortId // ignore: cast_nullable_to_non_nullable
as String,toPortId: null == toPortId ? _self.toPortId : toPortId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [Connection].
extension ConnectionPatterns on Connection {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Connection value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Connection() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Connection value)  $default,){
final _that = this;
switch (_that) {
case _Connection():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Connection value)?  $default,){
final _that = this;
switch (_that) {
case _Connection() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String fromNodeId,  String toNodeId,  String fromPortId,  String toPortId)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Connection() when $default != null:
return $default(_that.id,_that.fromNodeId,_that.toNodeId,_that.fromPortId,_that.toPortId);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String fromNodeId,  String toNodeId,  String fromPortId,  String toPortId)  $default,) {final _that = this;
switch (_that) {
case _Connection():
return $default(_that.id,_that.fromNodeId,_that.toNodeId,_that.fromPortId,_that.toPortId);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String fromNodeId,  String toNodeId,  String fromPortId,  String toPortId)?  $default,) {final _that = this;
switch (_that) {
case _Connection() when $default != null:
return $default(_that.id,_that.fromNodeId,_that.toNodeId,_that.fromPortId,_that.toPortId);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Connection implements Connection {
  const _Connection({required this.id, required this.fromNodeId, required this.toNodeId, required this.fromPortId, required this.toPortId});
  factory _Connection.fromJson(Map<String, dynamic> json) => _$ConnectionFromJson(json);

@override final  String id;
@override final  String fromNodeId;
@override final  String toNodeId;
@override final  String fromPortId;
@override final  String toPortId;

/// Create a copy of Connection
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ConnectionCopyWith<_Connection> get copyWith => __$ConnectionCopyWithImpl<_Connection>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ConnectionToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Connection&&(identical(other.id, id) || other.id == id)&&(identical(other.fromNodeId, fromNodeId) || other.fromNodeId == fromNodeId)&&(identical(other.toNodeId, toNodeId) || other.toNodeId == toNodeId)&&(identical(other.fromPortId, fromPortId) || other.fromPortId == fromPortId)&&(identical(other.toPortId, toPortId) || other.toPortId == toPortId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,fromNodeId,toNodeId,fromPortId,toPortId);

@override
String toString() {
  return 'Connection(id: $id, fromNodeId: $fromNodeId, toNodeId: $toNodeId, fromPortId: $fromPortId, toPortId: $toPortId)';
}


}

/// @nodoc
abstract mixin class _$ConnectionCopyWith<$Res> implements $ConnectionCopyWith<$Res> {
  factory _$ConnectionCopyWith(_Connection value, $Res Function(_Connection) _then) = __$ConnectionCopyWithImpl;
@override @useResult
$Res call({
 String id, String fromNodeId, String toNodeId, String fromPortId, String toPortId
});




}
/// @nodoc
class __$ConnectionCopyWithImpl<$Res>
    implements _$ConnectionCopyWith<$Res> {
  __$ConnectionCopyWithImpl(this._self, this._then);

  final _Connection _self;
  final $Res Function(_Connection) _then;

/// Create a copy of Connection
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? fromNodeId = null,Object? toNodeId = null,Object? fromPortId = null,Object? toPortId = null,}) {
  return _then(_Connection(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,fromNodeId: null == fromNodeId ? _self.fromNodeId : fromNodeId // ignore: cast_nullable_to_non_nullable
as String,toNodeId: null == toNodeId ? _self.toNodeId : toNodeId // ignore: cast_nullable_to_non_nullable
as String,fromPortId: null == fromPortId ? _self.fromPortId : fromPortId // ignore: cast_nullable_to_non_nullable
as String,toPortId: null == toPortId ? _self.toPortId : toPortId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
