import 'dart:ui';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/node_type.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/offset_json_converter.dart';

part 'node.freezed.dart';
part 'node.g.dart';

/// A node in a journey.
@freezed
abstract class Node with _$Node {
  /// Creates a new instance of the [Node].
  const factory Node({
    required String id,
    required NodeType type,
    @OffsetJsonConverter() required Offset position,
  }) = _Node;

  /// Creates a new instance of the [Node] from a JSON object.
  factory Node.fromJson(Map<String, dynamic> json) => _$NodeFromJson(json);
}
