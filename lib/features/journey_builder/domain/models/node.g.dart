// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'node.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Node _$NodeFromJson(Map<String, dynamic> json) => _Node(
  id: json['id'] as String,
  type: $enumDecode(_$NodeTypeEnumMap, json['type']),
  position: const OffsetJsonConverter().fromJson(
    json['position'] as Map<String, dynamic>,
  ),
);

Map<String, dynamic> _$NodeToJson(_Node instance) => <String, dynamic>{
  'id': instance.id,
  'type': _$NodeTypeEnumMap[instance.type]!,
  'position': const OffsetJsonConverter().toJson(instance.position),
};

const _$NodeTypeEnumMap = {
  NodeType.start: 'start',
  NodeType.end: 'end',
  NodeType.decision: 'decision',
  NodeType.action: 'action',
};
