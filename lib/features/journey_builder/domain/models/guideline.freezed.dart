// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'guideline.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Guideline {

/// The conditions for the guideline.
 List<Condition> get conditions;/// The action to be taken if the conditions are met.
 Action get action;
/// Create a copy of Guideline
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$GuidelineCopyWith<Guideline> get copyWith => _$GuidelineCopyWithImpl<Guideline>(this as Guideline, _$identity);

  /// Serializes this Guideline to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Guideline&&const DeepCollectionEquality().equals(other.conditions, conditions)&&(identical(other.action, action) || other.action == action));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(conditions),action);

@override
String toString() {
  return 'Guideline(conditions: $conditions, action: $action)';
}


}

/// @nodoc
abstract mixin class $GuidelineCopyWith<$Res>  {
  factory $GuidelineCopyWith(Guideline value, $Res Function(Guideline) _then) = _$GuidelineCopyWithImpl;
@useResult
$Res call({
 List<Condition> conditions, Action action
});


$ActionCopyWith<$Res> get action;

}
/// @nodoc
class _$GuidelineCopyWithImpl<$Res>
    implements $GuidelineCopyWith<$Res> {
  _$GuidelineCopyWithImpl(this._self, this._then);

  final Guideline _self;
  final $Res Function(Guideline) _then;

/// Create a copy of Guideline
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? conditions = null,Object? action = null,}) {
  return _then(_self.copyWith(
conditions: null == conditions ? _self.conditions : conditions // ignore: cast_nullable_to_non_nullable
as List<Condition>,action: null == action ? _self.action : action // ignore: cast_nullable_to_non_nullable
as Action,
  ));
}
/// Create a copy of Guideline
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ActionCopyWith<$Res> get action {
  
  return $ActionCopyWith<$Res>(_self.action, (value) {
    return _then(_self.copyWith(action: value));
  });
}
}


/// Adds pattern-matching-related methods to [Guideline].
extension GuidelinePatterns on Guideline {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Guideline value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Guideline() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Guideline value)  $default,){
final _that = this;
switch (_that) {
case _Guideline():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Guideline value)?  $default,){
final _that = this;
switch (_that) {
case _Guideline() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( List<Condition> conditions,  Action action)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Guideline() when $default != null:
return $default(_that.conditions,_that.action);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( List<Condition> conditions,  Action action)  $default,) {final _that = this;
switch (_that) {
case _Guideline():
return $default(_that.conditions,_that.action);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( List<Condition> conditions,  Action action)?  $default,) {final _that = this;
switch (_that) {
case _Guideline() when $default != null:
return $default(_that.conditions,_that.action);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Guideline implements Guideline {
  const _Guideline({required final  List<Condition> conditions, required this.action}): _conditions = conditions;
  factory _Guideline.fromJson(Map<String, dynamic> json) => _$GuidelineFromJson(json);

/// The conditions for the guideline.
 final  List<Condition> _conditions;
/// The conditions for the guideline.
@override List<Condition> get conditions {
  if (_conditions is EqualUnmodifiableListView) return _conditions;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_conditions);
}

/// The action to be taken if the conditions are met.
@override final  Action action;

/// Create a copy of Guideline
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$GuidelineCopyWith<_Guideline> get copyWith => __$GuidelineCopyWithImpl<_Guideline>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$GuidelineToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Guideline&&const DeepCollectionEquality().equals(other._conditions, _conditions)&&(identical(other.action, action) || other.action == action));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_conditions),action);

@override
String toString() {
  return 'Guideline(conditions: $conditions, action: $action)';
}


}

/// @nodoc
abstract mixin class _$GuidelineCopyWith<$Res> implements $GuidelineCopyWith<$Res> {
  factory _$GuidelineCopyWith(_Guideline value, $Res Function(_Guideline) _then) = __$GuidelineCopyWithImpl;
@override @useResult
$Res call({
 List<Condition> conditions, Action action
});


@override $ActionCopyWith<$Res> get action;

}
/// @nodoc
class __$GuidelineCopyWithImpl<$Res>
    implements _$GuidelineCopyWith<$Res> {
  __$GuidelineCopyWithImpl(this._self, this._then);

  final _Guideline _self;
  final $Res Function(_Guideline) _then;

/// Create a copy of Guideline
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? conditions = null,Object? action = null,}) {
  return _then(_Guideline(
conditions: null == conditions ? _self._conditions : conditions // ignore: cast_nullable_to_non_nullable
as List<Condition>,action: null == action ? _self.action : action // ignore: cast_nullable_to_non_nullable
as Action,
  ));
}

/// Create a copy of Guideline
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ActionCopyWith<$Res> get action {
  
  return $ActionCopyWith<$Res>(_self.action, (value) {
    return _then(_self.copyWith(action: value));
  });
}
}

// dart format on
