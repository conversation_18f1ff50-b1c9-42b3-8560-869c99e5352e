import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/action.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/condition.dart';

part 'guideline.freezed.dart';
part 'guideline.g.dart';

/// Represents a guideline in the journey.
@freezed
abstract class Guideline with _$Guideline {
  /// Creates a new instance of [Guideline].
  const factory Guideline({
    /// The conditions for the guideline.
    required List<Condition> conditions,

    /// The action to be taken if the conditions are met.
    required Action action,
  }) = _Guideline;

  /// Creates a new instance of [Guideline] from a JSON object.
  factory Guideline.fromJson(Map<String, dynamic> json) =>
      _$GuidelineFromJson(json);
}
