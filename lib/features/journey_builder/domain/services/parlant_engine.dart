import 'package:parlant_flutter/features/journey_builder/domain/models/action.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/condition.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/guideline.dart';

/// A simple engine for processing guidelines.
class ParlantEngine {
  /// Returns the action for the first guideline that matches the input.
  Action? getActionForInput(
    Map<String, dynamic> input,
    List<Guideline> guidelines,
  ) {
    for (final guideline in guidelines) {
      if (_conditionsAreMet(input, guideline.conditions)) {
        return guideline.action;
      }
    }
    return null;
  }

  bool _conditionsAreMet(
    Map<String, dynamic> input,
    List<Condition> conditions,
  ) {
    for (final condition in conditions) {
      if (!_conditionIsMet(input, condition)) {
        return false;
      }
    }
    return true;
  }

  bool _conditionIsMet(Map<String, dynamic> input, Condition condition) {
    final factValue = input[condition.fact];
    final conditionValue = condition.value;

    switch (condition.operator) {
      case 'equal':
        return factValue == conditionValue;
      case 'notEqual':
        return factValue != conditionValue;
      case 'in':
        return conditionValue is List && conditionValue.contains(factValue);
      case 'notIn':
        return conditionValue is List && !conditionValue.contains(factValue);
      case 'lessThan':
        return (factValue is num && conditionValue is num) &&
            (factValue < conditionValue);
      case 'greaterThan':
        return (factValue is num && conditionValue is num) &&
            (factValue > conditionValue);
      default:
        return false;
    }
  }
}
