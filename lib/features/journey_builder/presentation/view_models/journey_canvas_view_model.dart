import 'package:fl_nodes/fl_nodes.dart' as fln;
import 'package:parlant_flutter/app/app.locator.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/journey.dart';
import 'package:parlant_flutter/features/journey_builder/domain/repositories/journey_repository.dart';
import 'package:stacked/stacked.dart';
import 'package:uuid/uuid.dart';

/// The view model for the journey canvas.
class JourneyCanvasViewModel extends BaseViewModel {
  /// The constructor for the journey canvas view model.
  JourneyCanvasViewModel();

  final JourneyRepository _journeyRepository = locator<JourneyRepository>();
  final _uuid = const Uuid();

  late final fln.FlNodeEditorController _nodeEditorController;

  /// The controller for the node editor.
  fln.FlNodeEditorController get nodeEditorController => _nodeEditorController;

  Journey? _journey;

  /// The current journey.
  Journey? get journey => _journey;

  /// Initialises the view model.
  Future<void> initialise() async {
    setBusy(true);
    _journey = await _journeyRepository.getJourney();
    if (_journey == null) {
      _journey = Journey(id: _uuid.v4(), nodes: [], connections: []);
      await _journeyRepository.saveJourney(_journey!);
    }
    _nodeEditorController = fln.FlNodeEditorController();
    _nodeEditorController.project.load(
      data: {
        'nodes': <Map<String, dynamic>>[],
        'connections': <Map<String, dynamic>>[],
      },
    );
    setBusy(false);
  }
}
