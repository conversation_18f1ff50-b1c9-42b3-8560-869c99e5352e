import 'package:parlant_flutter/app/app.locator.dart';
import 'package:parlant_flutter/features/journey_builder/domain/services/placeholder_engine_service.dart';
import 'package:parlant_flutter/features/journey_builder/presentation/views/hello_world_view.dart';
import 'package:stacked/stacked.dart';

/// ViewModel for the [HelloWorldView].
class HelloWorldViewModel extends ReactiveViewModel {
  final PlaceholderEngineService _engineService =
      locator<PlaceholderEngineService>();

  String _result = 'Press the button!';

  /// The result of the placeholder engine processing.
  String get result => _result;

  /// Runs the placeholder engine process and updates the result.
  void runProcess() {
    _result = _engineService.processValue('Hello World');
    rebuildUi();
  }
}
