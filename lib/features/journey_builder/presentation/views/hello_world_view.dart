import 'package:flutter/material.dart';
import 'package:parlant_flutter/features/journey_builder/presentation/viewmodels/hello_world_viewmodel.dart';
import 'package:stacked/stacked.dart';

/// A view that demonstrates a simple end-to-end data flow.
class HelloWorldView extends StackedView<HelloWorldViewModel> {
  /// Creates a new [HelloWorldView].
  const HelloWorldView({super.key});

  @override
  Widget builder(
    BuildContext context,
    HelloWorldViewModel viewModel,
    Widget? child,
  ) {
    return Scaffold(
      appBar: AppBar(title: const Text('Hello World')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              viewModel.result,
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const Sized<PERSON><PERSON>(height: 24),
            ElevatedButton(
              onPressed: viewModel.runProcess,
              child: const Text('Run Process'),
            ),
          ],
        ),
      ),
    );
  }

  @override
  HelloWorldViewModel viewModelBuilder(
    BuildContext context,
  ) => HelloWorldViewModel();
}
