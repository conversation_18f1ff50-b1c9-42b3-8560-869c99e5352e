import 'package:fl_nodes/fl_nodes.dart';
import 'package:flutter/material.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/node_type.dart';
import 'package:parlant_flutter/features/journey_builder/presentation/view_models/journey_canvas_view_model.dart';
import 'package:parlant_flutter/features/journey_builder/presentation/widgets/node_palette.dart';
import 'package:stacked/stacked.dart';

/// The view for the journey canvas.
class JourneyCanvasView extends StatelessWidget {
  /// The constructor for the journey canvas view.
  const JourneyCanvasView({super.key});

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<JourneyCanvasViewModel>.reactive(
      viewModelBuilder: JourneyCanvasViewModel.new,
      onViewModelReady: (viewModel) => viewModel.initialise(),
      builder: (context, viewModel, child) {
        return Scaffold(
          appBar: AppBar(title: const Text('Journey Builder')),
          body: Row(
            children: [
              const Node<PERSON>alet<PERSON>(),
              Expanded(
                child: DragTarget<NodeType>(
                  onAcceptWithDetails: (details) {
                    // final offset = details.offset;
                    // viewModel.addNode(details.data, offset);
                  },
                  builder: (context, candidateData, rejectedData) {
                    return viewModel.isBusy
                        ? const Center(child: CircularProgressIndicator())
                        : FlNodeEditorWidget(
                            controller: viewModel.nodeEditorController,
                            overlay: () => [],
                          );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
