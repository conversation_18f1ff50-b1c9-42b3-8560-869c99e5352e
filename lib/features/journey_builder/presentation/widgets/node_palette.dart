import 'package:flutter/material.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/node_type.dart';

/// A widget that displays a palette of nodes that can be dragged onto the
/// canvas.
class NodePalette extends StatelessWidget {
  /// Creates a new instance of the [NodePalette].
  const NodePalette({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.grey[200],
      padding: const EdgeInsets.all(8),
      child: Column(
        children: [
          const Text(
            'Node Palette',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          ...NodeType.values.map(
            (type) => Draggable<NodeType>(
              data: type,
              feedback: Material(
                child: _buildPaletteItem(type, isDragging: true),
              ),
              child: _buildPaletteItem(type),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaletteItem(NodeType type, {bool isDragging = false}) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isDragging ? Colors.blue.withAlpha(128) : Colors.blue,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        type.name,
        style: const TextStyle(color: Colors.white),
      ),
    );
  }
}
