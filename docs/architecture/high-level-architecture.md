# High Level Architecture

## Technical Summary

This architecture outlines a modern, client-side Flutter application designed for cross-platform deployment. It uses a modular, three-layer architecture (Presentation, Domain, Data) to ensure a clean separation of concerns. The user interface and dependency management will be managed using the **MVVM pattern and service locator provided by the `stacked` package**. Data persistence will be handled locally on the device using `surrealdb_wasm`, and external communication with LLM providers will be managed through a dedicated, abstracted service layer. This approach directly supports the PRD goals of creating a maintainable, scalable, and user-friendly application on a modern, cross-platform technology stack.

## Platform and Infrastructure Choice

As this is a client-side application, the primary "platform" is the user's device (iOS, Android, Windows, macOS, Linux). The "infrastructure" for distribution will be the respective platform app stores and direct downloads for desktop.

* **Platform:** User device (Cross-Platform via Flutter)
* **Key Services:** The application will not rely on a dedicated backend infrastructure. Instead, it will directly consume external, third-party services:
    * LLM Provider APIs (e.g., OpenAI, Anthropic)
* **Deployment Host:** Not applicable (client-side).

## Repository Structure

* **Structure:** Single Package.
* **Rationale:** The project will be generated using the Very Good CLI `flutter_package` template. This provides a robust, standardized structure for a single, high-quality Flutter package/application. While not a multi-package monorepo, it enforces a clean organization of code, tests, and dependencies within one repository. The core application code will reside within the `lib/` directory, structured by feature and layer (Data, Domain, Presentation).

## Performance and Scalability Strategy

To address the potential performance limitations of a purely client-side engine, the architecture will incorporate a key design principle: **the engine's core processing logic will be abstracted**. This means the `Domain Layer` will be designed in such a way that its computational tasks can be executed by either a local implementation (running on the device) or a remote one (via an API call). This hybrid approach provides the best of both worlds:
* **Default:** Fast, offline-capable processing for standard tasks on the user's device.
* **Future-Proofing:** The ability to offload computationally intensive journeys or leverage more powerful server-side models in the future without requiring a full architectural rewrite.

## High Level Architecture Diagram

```mermaid
graph TD
    subgraph User Device
        subgraph Flutter Application
            A[Presentation Layer (UI - Stacked Views/ViewModels)]
            B[Domain Layer (Parlant Engine - UseCases)]
            C[Data Layer (Repositories)]
        end
        D[Local Database (surrealdb_wasm)]
    end

    E[External LLM APIs (OpenAI, etc.)]

    User -- Interacts with --> A
    A -- Calls --> B
    B -- Uses --> C
    C -- manages --> D
    C -- Fetches from --> E
```

## Architectural Patterns

  * **Modular 3-Layer Architecture:** As defined in the project guide, we will separate concerns into `Presentation`, `Domain`, and `Data` layers. This is fundamental to the project's organization and maintainability.
  * **Model-View-ViewModel (MVVM):** The chosen state management package, `stacked`, is a powerful implementation of the MVVM pattern. **It also provides a complete solution for dependency injection via its service locator, which simplifies service management throughout the application.**
  * **Repository Pattern:** The Data layer will use repositories to abstract the data sources (`surrealdb_wasm` and external APIs), providing a clean API for the Domain layer to use without needing to know the implementation details of data fetching or storage.
  * **Adapter Pattern:** A dedicated service layer will use this pattern to create a common interface for interacting with different LLM provider APIs, allowing us to easily add new providers in the future without changing the core engine logic.
