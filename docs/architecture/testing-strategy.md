# Testing Strategy

## **1. Testing Pyramid**

Our testing philosophy will follow the standard testing pyramid. We will have a large base of fast and isolated unit tests, a healthy number of widget tests for our UI, and a small, focused set of end-to-end tests for our most critical user journeys.

```text
      / \
     / _ \      <-- End-to-End Tests (few, slow, integrated) [patrol]
    /_____\
   / _ _ _ \    <-- Widget Tests (many, medium speed) [flutter_test]
  /_________\
 / _ _ _ _ _ \  <-- Unit Tests (most, fast, isolated) [flutter_test]
/_____________\
```

## **2. Test Organization**

All tests will reside in the `test/` directory, which will mirror the structure of the `lib/` directory.

  * **Frontend Tests (Widget Tests):**
      * **Location:** `test/features/journey_builder/presentation/`
      * **Purpose:** To test individual Flutter widgets and views in isolation from services. We will verify that the UI renders correctly based on a given state and that it responds to user interactions.
  * **Backend Tests (Unit Tests):**
      * **Location:** `test/features/journey_builder/domain/` and `test/features/journey_builder/data/`
      * **Purpose:** To test our pure Dart logic. This includes the `ParlantEngine`, all services, and repositories. All external dependencies (like the database or HTTP client) will be mocked to ensure the tests are fast and isolated.
  * **Data Layer Testing Strategy:**
      * When unit testing our repositories (e.g., `JourneyRepository`, `LLMRepository`), we will mock their lowest-level dependencies. For example, when testing the `LLMRepository`, we will provide a mock version of the `dio` client to simulate API responses and failures. This ensures we are testing the repository's data mapping and logic in isolation.
  * **Visual Regression Testing (Golden Files):**
      * For our key shared UI components (`lib/ui/shared/`), we will create "golden file" tests. This practice involves generating a reference image of a widget and saving it. On subsequent test runs, a new image is generated and compared against the "golden" one to catch any unintended visual changes (e.g., color, padding, layout).
  * **E2E Tests:**
      * **Location:** `integration_test/` directory.
      * **Purpose:** The primary goal of our E2E test suite is to validate the complete, core user journey. The initial suite **must** cover the following scenario:
        1.  Launch the app.
        2.  Add at least two nodes to the canvas.
        3.  Connect the nodes.
        4.  Configure the nodes via the properties panel.
        5.  Execute the journey from the test panel.
        6.  Verify that the correct output is displayed in the output node.

## **3. Test Examples**

### **Frontend Component Test (Widget Test)**

```dart
// test/features/journey_builder/presentation/widgets/node_widget_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:parlant_flutter/features/journey_builder/presentation/widgets/node_widget.dart';

void main() {
  testWidgets('NodeWidget displays the node text', (tester) async {
    await tester.pumpWidget(
      const MaterialApp(home: NodeWidget(text: 'My Node')),
    );

    expect(find.text('My Node'), findsOneWidget);
  });
}
```

### **Backend Logic Test (Unit Test)**

```dart
// test/features/journey_builder/domain/services/parlant_engine_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart'; // A popular mocking library
import 'package:parlant_flutter/features/journey_builder/domain/services/parlant_engine.dart';

// Create a mock for the repository dependency
class MockLLMRepository extends Mock implements LLMRepository {}

void main() {
  test('ParlantEngine should return a simple reply from a guideline', () {
    // Arrange
    final mockRepo = MockLLMRepository();
    final engine = ParlantEngine(llmRepository: mockRepo);
    // ... setup guidelines and input ...

    // Act
    final result = engine.execute(journey, input);

    // Assert
    expect(result.output, 'Expected Reply');
  });
}
```

### **Visual Regression Test (Golden File Example)**

```dart
// test/ui/shared/custom_button_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:parlant_flutter/ui/shared/custom_button.dart';

void main() {
  testWidgets('CustomButton matches golden file', (tester) async {
    await tester.pumpWidget(
      const MaterialApp(home: CustomButton(label: 'Submit')),
    );

    // This compares the rendered widget against a saved image file
    await expectLater(
      find.byType(CustomButton),
      matchesGoldenFile('goldens/custom_button.png'),
    );
  });
}
```

### **E2E Test (`patrol`)**

```dart
// integration_test/app_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:patrol/patrol.dart';
import 'package:parlant_flutter/main.dart'; // Your app's entry point

void main() {
  patrolTest('User can create and run a simple journey',
    (PatrolIntegrationTester $) async {
      await $.pumpWidgetAndSettle(const MyApp());
      
      // Use Patrol's custom finders to interact with the app
      await $(#addNodeButton).tap();
      await $.native.enterText(find.byType(TextField), text: 'Hello, world!');
      await $(#runButton).tap();

      // Assert that the expected output appears
      expect($('Hello, world!-processed'), findsOneWidget);
    },
  );
}
```
