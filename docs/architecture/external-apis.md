# External APIs

## 1\. LLM Provider API

  * **Purpose:** To provide the core generative AI capabilities for the Parlant engine via its `LLMNode`.
  * **Authentication:** `Bearer Token`.
  * **Rate Limits:** Provider-specific. Our application must handle `429 Too Many Requests` errors gracefully.
  * **Key Endpoints Used:** `POST /v1/chat/completions`.

## 2\. User-Defined Tool APIs

  * **Purpose:** To allow the agent to interact with any external service the user configures via a `ToolNode`.
  * **Supported Protocols:** RESTful APIs that accept and return `application/json`.
  * **Authentication:** We will support No Authentication, API Key in a custom header, and Bearer Token.
