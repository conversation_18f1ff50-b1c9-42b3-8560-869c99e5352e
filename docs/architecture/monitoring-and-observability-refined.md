# Monitoring and Observability (Refined)

## **1. User Privacy and Consent (New)**

The collection of any analytics or diagnostic data is a privilege, not a right.
* **Opt-In Consent:** The application **must** obtain explicit, opt-in consent from the user before enabling any analytics (Firebase) or error reporting (Sentry) services.
* **Anonymization:** All data sent to analytics services must be fully anonymized. We will not track any Personally Identifiable Information (PII).
* **Opt-Out:** The application settings will provide a clear option for the user to disable data collection at any time.

## **2. Monitoring Stack**

For our client-side application, the monitoring stack will be focused on error reporting, performance, and user analytics.

* **Frontend Monitoring (Analytics):** We will use **Firebase Analytics** (or Google Analytics for Firebase) to gather anonymized data on user engagement, feature adoption, and user flows.
* **Backend Monitoring (Client-Side):** The health of our client-side engine will be monitored via the tools below, not traditional server monitoring.
* **Error Tracking:** We will use **Sentry**, as defined in our tech stack. Our `ErrorReporterService` will send all unhandled exceptions and significant failures to Sentry for real-time alerts and debugging.
* **Performance Monitoring:** We will use **Sentry Performance Monitoring** to track application startup times, screen transition performance, and identify slow operations within the application.

## **3. Configuration Management (New)**

To ensure data from different environments is kept separate, we will use our **Flutter Flavors** system to manage monitoring configurations.
* The Sentry DSN (API key) and the Firebase configuration file (`google-services.json` / `GoogleService-Info.plist`) will be different for the `dev`, `staging`, and `prod` flavors.
* This guarantees that debug data is sent to a separate Sentry/Firebase project and never pollutes our production metrics.

## **4. Data Privacy and Scrubbing (New)**

To protect user privacy, we will enforce a strict policy on what data is allowed to leave the device.
* **Strict No-Log Rule:** Under no circumstances should any user-generated content be sent to external monitoring services. This includes, but is not limited to:
    * Text from prompts or nodes.
    * LLM API responses.
    * User-configured Tool details.
    * User-provided API keys.
* **Client-Side Scrubbing:** The Sentry client will be configured to automatically scrub all application state and view hierarchy data from error reports before they are sent. We will only send the exception and the relevant, non-sensitive parts of the stack trace.

## **5. Key Metrics**

We will track the following key metrics to measure the health and success of our application:

* **Frontend Metrics:**
    * **User Engagement:** Daily Active Users (DAU), user retention rates, average session length.
    * **Feature Adoption:** Frequency of key events, such as "Journey Created," "Journey Executed," and usage counts for each type of `Node`.
    * **Stability:** Crash-free user percentage (via Sentry).
    * **Performance:** App startup time, average screen load times (via Sentry).
* **Backend (Client-Side Engine) Metrics:**
    * **Error Rate:** The percentage of journey executions that result in a `Failure`. This will be tracked as a custom event in Sentry.
    * **Performance:** The average execution time for a journey. This can be tracked with Sentry Performance Monitoring.
    * **API Interaction:** Success/failure rates and average latency for calls made by the `LLMRepository` and `ToolRepository`. This is critical for monitoring our external dependencies and can be captured by Sentry.
