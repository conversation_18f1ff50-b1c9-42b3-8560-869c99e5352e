# Core Workflows

This sequence diagram illustrates the end-to-end process when a user runs a journey, including the error handling path.

```mermaid
sequenceDiagram
    actor User; participant TPV as TestPanelView; participant TPVM as TestPanelViewModel; participant <PERSON><PERSON> as JourneyOrchestrationService; participant <PERSON><PERSON> as ParlantEngine; participant <PERSON><PERSON> as LLMRepository; participant E<PERSON> as ExternalLLMAPI;
    User->>TPV: 1. Clicks "Run"
    TPV->>TPVM: 2. Calls run<PERSON><PERSON><PERSON>(input)
    TPVM->>JOS: 3. Calls runJ<PERSON>ney(input)
    activate JOS; JOS->>PE: 4. Calls execute(journey, input)
    note over PE: Internally creates an 'ExecutionContext' to manage this single run.
    activate PE; PE->>LR: 5. Calls getCompletion(prompt)
    activate LR; LR->>EXT: 6. Makes async HTTP request
    alt Successful API Call
        EXT-->>LR: 7a. Returns raw API response (e.g., JSON)
        note over LR: Maps raw JSON to clean, internal 'LLMResult' domain model.
        LR-->>PE: 8a. Returns Success(LLMResult)
        note over PE: Engine processes result and logs step in 'ExecutionContext'.
        PE-->>JOS: 9a. Returns Success(ExecutionResult)
        note over JOS: 'ExecutionResult' contains both the final output AND the full execution trace for explainability.
        JOS->>JOS: 10a. Updates state with ExecutionResult
        deactivate PE
        note over JOS, TPVM: 11a. JOS emits new state on a Stream
        TPVM->>TPV: 12a. UI rebuilds to show result and trace log
        TPV->>User: 13a. Displays final output and explainability steps
    else API Call Fails
        EXT-->>LR: 7b. Returns HTTP Error
        note over LR: Maps HTTP error to a clean, internal 'ApiFailure' domain model.
        LR-->>PE: 8b. Returns Failure(ApiFailure)
        PE-->>JOS: 9b. Propagates Failure(ApiFailure)
        deactivate PE
        JOS->>JOS: 10b. Updates state with error
        note over JOS, TPVM: 11b. JOS emits error state on the Stream
        TPVM->>TPV: 12b. UI rebuilds to show error state
        TPV->>User: 13b. Displays user-friendly error message
    end
    deactivate JOS; deactivate LR;
```
