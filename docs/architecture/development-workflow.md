# Development Workflow

This section provides the practical, step-by-step instructions a developer will follow to set up the project, run tests, and manage their local environment. This is based on the standards provided by the Very Good CLI template.

## **1. Local Development Setup**

### **Prerequisites**

Before working on this project, developers must have the following tools installed:

  * Flutter SDK (latest stable version)
  * Very Good CLI: `dart pub global activate very_good_cli`

### **Initial Setup**

After cloning the repository for the first time, run the following command from the project root to fetch all dependencies:

```bash
flutter pub get
```

### **Development Commands**

This project uses the standard tooling provided by Flutter and the Very Good CLI template.

```bash
# Run all tests and generate a coverage report
flutter test --coverage

# Check for outdated dependencies
flutter pub outdated

# Run the application on a connected device
# (e.g., iOS Simulator, Android Emulator, or Desktop)
flutter run

# Check code for analysis issues and linter warnings
flutter analyze
```

## **2. Environment Configuration**

We will manage environment variables (like API keys) using a `.env` file, which should not be committed to source control. The `flutter_dotenv` package will be used to load these variables at runtime.

### **Required Environment Variables**

Developers will need to create a `.env` file in the project root by copying the `.env.example` file.

**`.env.example` file:**

```bash
# This file lists all required environment variables for the application.
# Copy this file to .env and fill in your own values.

# -- LLM Provider Configuration --
# The API key for your chosen LLM provider (e.g., OpenAI)
# This is retrieved by the LLMRepository.
OPENAI_API_KEY=YOUR_API_KEY_HERE

# The base URL for the LLM provider API.
# This allows for easy switching or proxying.
OPENAI_BASE_URL=[https://api.openai.com/v1](https://api.openai.com/v1)
```
