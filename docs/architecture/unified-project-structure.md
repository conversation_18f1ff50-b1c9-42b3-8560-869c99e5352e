# Unified Project Structure

This section provides a definitive folder layout for our application. It combines the high-quality foundation from the Very Good CLI `flutter_package` template with the feature-first, Clean Architecture structure we have designed.

```plaintext
parlant_flutter/
├── .github/                  # CI/CD workflows from Very Good CLI
│   └── workflows/
│       └── main.yaml
├── lib/
│   ├── app/                    # App-level setup (routing, dependency injection)
│   │   ├── app.dart            # Stacked router and service definitions
│   │   └── app.locator.dart    # Generated by stacked_generator
│   │
│   ├── features/               # Feature-first directories
│   │   └── journey_builder/    # The main feature of our application
│   │       ├── application/  // Refined: New folder for application services
│   │       │   └── journey_orchestration_service.dart
│   │       │
│   │       ├── data/
│   │       │   ├── dtos/       // Refined: For data transfer objects if needed
│   │       │   └── repositories/
│   │       │
│   │       ├── domain/
│   │       │   ├── models/     // Refined: Core models moved to Domain layer
│   │       │   ├── repositories/ // Interfaces (abstract classes) for repositories
│   │       │   └── services/     // ParlantEngine
│   │       │
│   │       └── presentation/
│   │           ├── views/        # journey_canvas_view.dart
│   │           ├── viewmodels/   # canvas_viewmodel.dart, properties_viewmodel.dart
│   │           └── widgets/      # Feature-specific widgets (node_widget.dart)
│   │
│   ├── ui/                     # Shared, application-wide UI components
│   │   └── shared/
│   │       ├── custom_button.dart
│   │       └── loading_indicator.dart
│   │
│   └── parlant_flutter.dart    # Main package entry file
│
├── test/                     # All tests, mirroring the lib/ structure
│   └── ... (mirrors the lib/ structure)
├── analysis_options.yaml     # Linting rules (from Very Good Core)
├── pubspec.yaml              # Project dependencies
└── README.md
```
