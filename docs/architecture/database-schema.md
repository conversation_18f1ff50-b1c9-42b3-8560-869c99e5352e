# Database Schema

This schema defines a normalized, relational structure within `surrealdb_wasm`, with separate tables for our main entities connected via Record Links.

An excellent decision. The initial schema is simple, but as an architect, I see areas where we can make it much more powerful and maintainable for the future.

## **Critique of the Current Schema**

The primary critique of the current "single document" schema is its handling of **reusable items** and **data normalization**.

For example, if you wanted to use the same `Guideline` or `Tool` in ten different journeys, you would have to copy and paste its full definition into each of the ten `journey` documents. If you later needed to update that single `Guideline`, you would have to find and manually update it in all ten places, which is inefficient and prone to errors.

## **Refined Database Schema**

To solve this, I propose a more relational or "normalized" schema that takes better advantage of SurrealDB's multi-model capabilities. We will create separate tables for our main entities and use SurrealDB's powerful **Record Links** to connect them. This is a far more robust and scalable approach.

**1. `journey` Table**

  * **Purpose:** Stores the top-level information for a journey.
  * **Example Record:**
    ```json
    {
        "id": "journey:b8sa4k2p9",
        "name": "My First Agent Journey"
    }
    ```

**2. `node` Table**

  * **Purpose:** Stores the definition for every individual node. Each node is linked back to its parent journey.
  * **Example Record:**
    ```json
    {
        "id": "node:def",
        "journey": "journey:b8sa4k2p9", // Record Link to the parent journey
        "type": "llm",
        "position": { "x": 300, "y": 150 },
        "data": {
            "prompt": "Summarize the following text: {{input}}",
            // Node-specific guidelines could be linked here
            "guidelines": [ "guideline:abc", "guideline:def" ] 
        }
    }
    ```

**3. `connection` Table**

  * **Purpose:** A simple table to define the relationships between nodes. SurrealDB is also a graph database, so we could use graph edges, but a simple table is also very effective.
  * **Example Record:**
    ```json
    {
        "id": "connection:123",
        "journey": "journey:b8sa4k2p9",
        "from": "node:abc", // Record Link to the source node
        "to": "node:def"   // Record Link to the target node
    }
    ```

**4. `guideline` Table**

  * **Purpose:** Stores a central library of reusable guidelines.
  * **Example Record:**
    ```json
    {
        "id": "guideline:abc",
        "name": "Handle Negative Sentiment",
        "condition": { "variable": "input_sentiment", "operator": "equals", "value": "negative" },
        "action": { "type": "reply", "text": "I'm sorry to hear that." },
        "salience": 0.5
    }
    ```

**5. `tool_definition` Table**

  * **Purpose:** Stores a central library of reusable tool definitions. A `ToolNode` would simply contain a record link to a tool in this table.
  * **Example Record:**
    ```json
    {
        "id": "tool:xyz",
        "name": "Get Weather",
        "description": "Fetches the current weather for a city.",
        "apiUrl": "https://api.weather.com/forecast",
        "authMethod": "api_key_header",
        "authKey": "X-API-KEY"
    }
    ```
