# Introduction

This document outlines the complete fullstack architecture for the Parlant Journey Builder, including the client-side Dart engine (backend logic), the Flutter UI (frontend), and their integration. It serves as the single source of truth for AI-driven development, ensuring consistency across the entire technology stack.

## Starter Template or Existing Project

The project is a new greenfield application. The PRD specifies that the foundation will be created using a starter template.

**Decision**: The project will be generated using the **Very Good CLI `flutter_package` template**. This choice establishes a high-quality foundation for the project structure, dependency management, linting rules, and automated testing, which this architecture will build upon.

## Change Log

| Date | Version | Description | Author |
| :--- | :--- | :--- | :--- | :--- |
| 2025-09-04 | 1.0 | Initial creation of the architecture document. | <PERSON> (Architect) |
