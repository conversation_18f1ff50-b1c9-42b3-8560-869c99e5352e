# Frontend Architecture

## **1. Component Architecture**

### **Component Organization**

To better scale our UI, we will distinguish between widgets that are specific to a feature and widgets that are shared across the application.

```text
lib/
├── features/
│   └── journey_builder/
│       └── presentation/
│           ├── views/
│           │   └── journey_canvas_view.dart
│           ├── viewmodels/
│           │   └── journey_canvas_viewmodel.dart
│           └── widgets/  // Widgets ONLY for the journey_builder feature
│               ├── node_widget.dart
│               └── connection_painter.dart
└── ui/
    └── shared/         // Globally reusable widgets
        ├── app_colors.dart
        ├── custom_button.dart
        └── loading_indicator.dart
```

### **View Template **

This template is more idiomatic for the `stacked` architecture and should be the standard for all new views.

```dart
import 'package:flutter/material.dart';
import 'package:stacked/stacked.dart';

import './my_cool_viewmodel.dart';

class MyCoolView extends StackedView<MyCoolViewModel> {
  const MyCoolView({super.key});

  @override
  Widget builder(
    BuildContext context,
    MyCoolViewModel viewModel,
    Widget? child,
  ) {
    return Scaffold(
      body: Center(
        child: Text(viewModel.title),
      ),
    );
  }

  @override
  MyCoolViewModel viewModelBuilder(
    BuildContext context,
  ) => MyCoolViewModel();
}
```

## **2. State Management Architecture**

As decided, we will use the `stacked` package.

### **State Structure**

The core of the `stacked` architecture is the pairing of a `View` (a widget) with a `ViewModel`.

  * `..._view.dart`: A `StatelessWidget` that uses a `ViewModelBuilder` to construct the UI. It contains no business logic.
  * `..._viewmodel.dart`: A class that extends `ReactiveViewModel`. It holds the state, contains all business logic for the view, and interacts with services.

### **State Management Patterns**

  * **Reactivity:** The `ViewModel` will notify the `View` to rebuild whenever its state changes.
  * **Service Locator:** All dependencies (like repositories or services) will be accessed from the `ViewModel` using the built-in `locator`.
  * **Business Logic:** All UI logic, state manipulation, and calls to services must reside in the `ViewModel`, not the `View`.

## **3. Routing Architecture**

### **Route Organization (Refined)**

The `app.dart` file is also where we will register all our services for dependency injection. The example has been expanded to be more complete and realistic for our application.

```dart
// lib/app/app.dart
import 'package:stacked/stacked_annotations.dart';
import 'package:stacked_services/stacked_services.dart';
// ... import views and services ...

@StackedApp(
  routes: [
    MaterialRoute(page: JourneyCanvasView, initial: true),
    MaterialRoute(page: SettingsView),
  ],
  dependencies: [
    // Core Services
    LazySingleton(classType: NavigationService),
    LazySingleton(classType: DialogService),
    
    // HTTP Client
    LazySingleton(classType: ApiClient),
    
    // Data Layer Repositories
    LazySingleton(classType: JourneyRepository),
    LazySingleton(classType: LLMRepository),
    LazySingleton(classType: ToolRepository),
    
    // Application & Domain Layer Services
    LazySingleton(classType: JourneyOrchestrationService),
    LazySingleton(classType: ParlantEngine),
  ]
)
class App {}
```

## **4. Frontend Services Layer**

This defines how the frontend communicates with the Data Layer and external APIs.

### **API Client Setup**

We will register our `dio` instance as a singleton service that can be injected into our repositories.

```dart
// In lib/app/app.dart dependencies list:
@LazySingleton()
Dio get dioService {
  final dio = Dio(BaseOptions(baseUrl: 'https://api.provider.com'));
  // Add interceptors for auth, logging, etc.
  return dio;
}
```

### **Service Example**

A repository in the Data Layer would then use this injected `dio` instance to make API calls.

```dart
// In a repository class
class LLMRepository {
  final _dio = locator<Dio>();

  Future<String> getCompletion() async {
    // ... make a call using _dio instance
  }
}
```
