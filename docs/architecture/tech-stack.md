# Tech Stack

| Category | Technology | Version | Purpose | Rationale |
| :--- | :--- | :--- | :--- | :--- |
| **Frontend Language** | Dart | latest stable | Primary language for all code | Required by Flutter; provides type safety and high performance. |
| **Frontend Framework**| Flutter | latest stable | UI toolkit and application framework | Chosen for its cross-platform capabilities and rich widget library. |
| **State Management** | stacked | latest stable | State management and dependency injection | **High team familiarity, which reduces risk and accelerates development.** |
| **Data Models** | freezed | latest stable | Code generation for immutable classes | Defined in the project guide; ensures robust, testable data models. |
| **Backend Language** | Dart | latest stable | Language for the ported Parlant engine | Unifies the tech stack, allowing code and model sharing between UI and logic. |
| **Local Database** | surrealdb\_wasm| latest stable | Local, on-device data persistence | **High team familiarity with SurrealDB. This choice mitigates adoption risk.**|
| **API Integration** | dio | latest stable | HTTP client for external API calls | Defined in the project guide; a standard and powerful networking client for Dart. |
| **Unit/Widget Testing**| flutter\_test | SDK version | Unit & widget testing for UI and logic | The default testing framework provided by the Very Good CLI template. |
| **E2E Testing** | patrol | latest stable | End-to-end testing and automation | Recommended for its robustness and integration with the Flutter ecosystem. |
| **CI/CD** | GitHub Actions | N/A | Continuous Integration & Deployment | The default setup provided by the Very Good CLI `flutter_package` template. |
| **Logging** | logger | latest stable | In-app logging for debugging | A lightweight and flexible logging utility mentioned in the PRD. |
| **Error Reporting** | Sentry | latest stable | Crash and error reporting for releases | Recommended for monitoring application health in production. |
