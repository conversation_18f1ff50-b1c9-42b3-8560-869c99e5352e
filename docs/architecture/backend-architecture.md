# Backend Architecture

## **1. Service Architecture**

Our application uses a client-side service architecture composed of the Application, Domain, and Data layers we defined in the Components section.

  * **Application Layer (`JourneyOrchestrationService`):** Orchestrates the application's workflows. It is the bridge between the UI and the core engine.
  * **Domain Layer (`ParlantEngine`):** Contains the pure, stateless, ported business logic of Parlant. It is the "brain" of the application.
  * **Data Layer (`Repositories`):** Manages all data operations, abstracting the details of the local database and external API calls from the rest of the application.

This layered structure ensures a clean separation of concerns and high testability for our client-side logic.

## **2. Database Architecture**

### **Schema Design**

As defined in our `Database Schema` section, we will use a normalized, relational structure within `surrealdb_wasm`, with separate tables for `journey`, `node`, `connection`, `guideline`, and `tool_definition`, all connected via Record Links. This prevents data duplication and provides a flexible foundation.

### **Data Access Layer (Refined)**

The Repository Pattern remains, but the interface will be more comprehensive to support all the features our UI will need.

```dart
// A more complete interface for the JourneyRepository
abstract class IJourneyRepository {
  Future<Journey?> getJourney(String id);
  Future<List<JourneyHeader>> getAllJourneyHeaders(); // More efficient for lists
  Future<void> saveJourney(Journey journey);
  Future<void> deleteJourney(String id);
}

// JourneyHeader would be a lightweight class with just id and name for list views.
```

## **3. Authentication Architecture (Refined)**

To support multiple LLM providers and user-defined Tools, we need a more dynamic approach to authentication.

### **New Component: `SecureStorageService`**

  * **Responsibility:** A dedicated service to securely manage a collection of API keys. It will store keys mapped to a unique identifier, like the API's base URL.
  * **Example Methods:** `saveKey(for: 'api.openai.com', key: '...')`, `getKey(for: 'api.openai.com')`.
  * **Technology:** It will use the `flutter_secure_storage` package as its underlying implementation.

### **Authentication Flow**

1.  The user saves multiple API keys in the settings view, each associated with a service identifier (e.g., its base URL).
2.  The `SecureStorageService` saves these keys securely on the device.
3.  The `DynamicAuthInterceptor` (see below) uses this service to find the correct key for each outgoing request.

### **Refined: `DynamicAuthInterceptor`**

The simple interceptor is replaced with a more intelligent one that can handle multiple services.

```dart
// A more robust Dio Interceptor for dynamic authentication
class DynamicAuthInterceptor extends Interceptor {
  final _secureStorageService; // Our new dedicated service

  DynamicAuthInterceptor(this._secureStorageService);

  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    // Identify the service being called by its base URL
    final serviceIdentifier = options.baseUrl;

    // Retrieve the specific key for that service
    final apiKey = await _secureStorageService.getKey(for: serviceIdentifier);

    if (apiKey != null) {
      // Logic to determine auth type (e.g., Bearer, X-API-Key)
      // This would be stored alongside the key.
      options.headers['Authorization'] = 'Bearer $apiKey';
    }

    return super.onRequest(options, handler);
  }
}
```
