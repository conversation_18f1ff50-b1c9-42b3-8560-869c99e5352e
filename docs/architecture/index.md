# Parlant Journey Builder Fullstack Architecture Document

## Table of Contents

- [Parlant Journey Builder Fullstack Architecture Document](#table-of-contents)
  - [Introduction](./introduction.md)
    - [Starter Template or Existing Project](./introduction.md#starter-template-or-existing-project)
    - [Change Log](./introduction.md#change-log)
  - [High Level Architecture](./high-level-architecture.md)
    - [Technical Summary](./high-level-architecture.md#technical-summary)
    - [Platform and Infrastructure Choice](./high-level-architecture.md#platform-and-infrastructure-choice)
    - [Repository Structure](./high-level-architecture.md#repository-structure)
    - [Performance and Scalability Strategy](./high-level-architecture.md#performance-and-scalability-strategy)
    - [High Level Architecture Diagram](./high-level-architecture.md#high-level-architecture-diagram)
    - [Architectural Patterns](./high-level-architecture.md#architectural-patterns)
  - [Tech Stack](./tech-stack.md)
  - [Data Models](./data-models.md)
    - [Application State Models](./data-models.md#application-state-models)
      - [1. Journey](./data-models.md#1-journey)
      - [2. Node](./data-models.md#2-node)
      - [3. Connection](./data-models.md#3-connection)
    - [Domain Logic Models](./data-models.md#domain-logic-models)
      - [4. Guideline](./data-models.md#4-guideline)
      - [5. Condition](./data-models.md#5-condition)
      - [6. Action](./data-models.md#6-action)
  - [API Specification](./api-specification.md)
  - [Components](./components.md)
    - [Presentation Layer](./components.md#presentation-layer)
    - [Application Layer](./components.md#application-layer)
    - [Domain Layer](./components.md#domain-layer)
    - [Data Layer](./components.md#data-layer)
    - [Final Component Diagram](./components.md#final-component-diagram)
  - [External APIs](./external-apis.md)
    - [1. LLM Provider API](./external-apis.md#1-llm-provider-api)
    - [2. User-Defined Tool APIs](./external-apis.md#2-user-defined-tool-apis)
  - [Core Workflows](./core-workflows.md)
  - [Database Schema](./database-schema.md)
    - [Critique of the Current Schema](./database-schema.md#critique-of-the-current-schema)
    - [Refined Database Schema](./database-schema.md#refined-database-schema)
  - [Frontend Architecture](./frontend-architecture.md)
    - [1. Component Architecture](./frontend-architecture.md#1-component-architecture)
      - [Component Organization](./frontend-architecture.md#component-organization)
      - [**View Template **](./frontend-architecture.md#view-template)
    - [2. State Management Architecture](./frontend-architecture.md#2-state-management-architecture)
      - [State Structure](./frontend-architecture.md#state-structure)
      - [State Management Patterns](./frontend-architecture.md#state-management-patterns)
    - [3. Routing Architecture](./frontend-architecture.md#3-routing-architecture)
      - [Route Organization (Refined)](./frontend-architecture.md#route-organization-refined)
    - [4. Frontend Services Layer](./frontend-architecture.md#4-frontend-services-layer)
      - [API Client Setup](./frontend-architecture.md#api-client-setup)
      - [Service Example](./frontend-architecture.md#service-example)
  - [Backend Architecture](./backend-architecture.md)
    - [1. Service Architecture](./backend-architecture.md#1-service-architecture)
    - [2. Database Architecture](./backend-architecture.md#2-database-architecture)
      - [Schema Design](./backend-architecture.md#schema-design)
      - [Data Access Layer (Refined)](./backend-architecture.md#data-access-layer-refined)
    - [3. Authentication Architecture (Refined)](./backend-architecture.md#3-authentication-architecture-refined)
      - [New Component: ](./backend-architecture.md#new-component)
      - [Authentication Flow](./backend-architecture.md#authentication-flow)
      - [Refined: ](./backend-architecture.md#refined)
  - [Unified Project Structure](./unified-project-structure.md)
  - [Development Workflow](./development-workflow.md)
    - [1. Local Development Setup](./development-workflow.md#1-local-development-setup)
      - [Prerequisites](./development-workflow.md#prerequisites)
      - [Initial Setup](./development-workflow.md#initial-setup)
      - [Development Commands](./development-workflow.md#development-commands)
    - [2. Environment Configuration](./development-workflow.md#2-environment-configuration)
      - [Required Environment Variables](./development-workflow.md#required-environment-variables)
  - [Deployment Architecture](./deployment-architecture.md)
    - [1. Deployment Strategy](./deployment-architecture.md#1-deployment-strategy)
    - [2. CI/CD Pipeline (Refined)](./deployment-architecture.md#2-cicd-pipeline-refined)
      - [Secrets Management](./deployment-architecture.md#secrets-management)
      - [Versioning and Build Numbering](./deployment-architecture.md#versioning-and-build-numbering)
      - [Deployment Automation](./deployment-architecture.md#deployment-automation)
    - [3. Build Flavors and Configurations (New)](./deployment-architecture.md#3-build-flavors-and-configurations-new)
    - [4. Desktop Deployment Strategy (New)](./deployment-architecture.md#4-desktop-deployment-strategy-new)
    - [5. Environments](./deployment-architecture.md#5-environments)
  - [Security and Performance](./security-and-performance.md)
    - [1. Security Requirements](./security-and-performance.md#1-security-requirements)
    - [2. Performance Optimization](./security-and-performance.md#2-performance-optimization)
  - [Testing Strategy](./testing-strategy.md)
    - [1. Testing Pyramid](./testing-strategy.md#1-testing-pyramid)
    - [2. Test Organization](./testing-strategy.md#2-test-organization)
    - [3. Test Examples](./testing-strategy.md#3-test-examples)
      - [Frontend Component Test (Widget Test)](./testing-strategy.md#frontend-component-test-widget-test)
      - [Backend Logic Test (Unit Test)](./testing-strategy.md#backend-logic-test-unit-test)
      - [Visual Regression Test (Golden File Example)](./testing-strategy.md#visual-regression-test-golden-file-example)
      - [E2E Test ()](./testing-strategy.md#e2e-test)
  - [Coding Standards](./coding-standards.md)
    - [Critical Fullstack Rules](./coding-standards.md#critical-fullstack-rules)
    - [Naming Conventions](./coding-standards.md#naming-conventions)
  - [Error Handling Strategy](./error-handling-strategy.md)
    - [1. Error Flow](./error-handling-strategy.md#1-error-flow)
    - [2. Error Response Format](./error-handling-strategy.md#2-error-response-format)
    - [3. Frontend Error Handling](./error-handling-strategy.md#3-frontend-error-handling)
    - [4. Backend (Data Layer) Error Handling](./error-handling-strategy.md#4-backend-data-layer-error-handling)
  - [Monitoring and Observability (Refined)](./monitoring-and-observability-refined.md)
    - [1. User Privacy and Consent (New)](./monitoring-and-observability-refined.md#1-user-privacy-and-consent-new)
    - [2. Monitoring Stack](./monitoring-and-observability-refined.md#2-monitoring-stack)
    - [3. Configuration Management (New)](./monitoring-and-observability-refined.md#3-configuration-management-new)
    - [4. Data Privacy and Scrubbing (New)](./monitoring-and-observability-refined.md#4-data-privacy-and-scrubbing-new)
    - [5. Key Metrics](./monitoring-and-observability-refined.md#5-key-metrics)
  - [Accessibility (a11y) Strategy](./accessibility-a11y-strategy.md)
    - [1. Compliance Target](./accessibility-a11y-strategy.md#1-compliance-target)
    - [2. Core Principles](./accessibility-a11y-strategy.md#2-core-principles)
    - [3. Implementation Guidelines](./accessibility-a11y-strategy.md#3-implementation-guidelines)
    - [4. Testing Strategy](./accessibility-a11y-strategy.md#4-testing-strategy)
