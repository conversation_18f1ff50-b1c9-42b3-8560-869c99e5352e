# API Specification

This section defines the contracts for the external APIs we will consume, primarily the LLM providers.

```yaml
openapi: 3.0.0
info:
  title: "Standardized LLM Provider API"
  version: "1.0.0"
  description: "A standardized contract for chat completion services that the Parlant application will consume. This allows different providers (OpenAI, Anthropic, etc.) to be used via an adapter."
servers:
  - url: "[https://api.provider.com](https://api.provider.com)"
    description: "This is a generic base URL; the actual URL will be configured in the app's settings."

paths:
  /v1/chat/completions:
    post:
      summary: "Get a chat completion from the LLM."
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatCompletionRequest'
      responses:
        '200':
          description: "Successful response from the LLM."
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatCompletionResponse'
        '401':
          description: "Unauthorized - API key is missing or invalid."

components:
  schemas:
    Message:
      type: object
      properties:
        role:
          type: string
          enum: [system, user, assistant]
        content:
          type: string

    ChatCompletionRequest:
      type: object
      properties:
        model:
          type: string
          example: "gpt-4"
        messages:
          type: array
          items:
            $ref: '#/components/schemas/Message'
    
    ChatCompletionResponse:
      type: object
      properties:
        id:
          type: string
        object:
          type: string
        created:
          type: integer
        model:
          type: string
        choices:
          type: array
          items:
            type: object
            properties:
              index:
                type: integer
              message:
                $ref: '#/components/schemas/Message'

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      description: "API Key provided by the LLM service."
```
