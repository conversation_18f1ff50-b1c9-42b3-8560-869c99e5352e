# Data Models

## **Application State Models**

*(These define the visual builder and the overall structure of the user's creation.)*

### 1\. Journey

**Purpose:** This is the top-level model that represents a complete, runnable agent configuration. It contains all the nodes, connections, and global rules.
**Dart Class (`freezed`):**

```dart
import 'package:freezed_annotation/freezed_annotation.dart';
import 'node.dart';
import 'connection.dart';
import 'guideline.dart';

part 'journey.freezed.dart';
part 'journey.g.dart';

@freezed
class Journey with _$Journey {
  const factory Journey({
    required String id,
    required String name,
    required List<Node> nodes,
    required List<Connection> connections,
    List<Guideline>? globalGuidelines,
  }) = _Journey;

  factory Journey.fromJson(Map<String, dynamic> json) => _$Journey<PERSON>(json);
}
```

### 2\. Node

**Purpose:** Represents a single step or component on the visual canvas. This is a union type (sealed class) to handle different kinds of nodes.
**Dart Class (`freezed`):**

```dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'node.freezed.dart';
part 'node.g.dart';

@freezed
class Node with _$Node {
  const factory Node.input({ required String id, required Offset position, @Default('Start') String text }) = InputNode;
  const factory Node.llm({ required String id, required Offset position, required String prompt }) = LLMNode;
  const factory Node.tool({ required String id, required Offset position, required String toolId }) = ToolNode;
  const factory Node.output({ required String id, required Offset position, @Default('End') String text }) = OutputNode;

  factory Node.fromJson(Map<String, dynamic> json) => _$NodeFromJson(json);
}
```

### 3\. Connection

**Purpose:** Represents the directional link between two nodes on the canvas, defining the flow of execution.
**Dart Class (`freezed`):**

```dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'connection.freezed.dart';
part 'connection.g.dart';

@freezed
class Connection with _$Connection {
  const factory Connection({
    required String id,
    required String sourceNodeId,
    required String targetNodeId,
  }) = _Connection;

  factory Connection.fromJson(Map<String, dynamic> json) => _$ConnectionFromJson(json);
}
```

-----

## **Domain Logic Models**

*(These are the Dart ports of the core Python classes from `parlant.txt`. They will be contained within the "data" property of the Nodes above.)*

### 4\. Guideline

**Purpose:** Represents a single rule for the agent to follow, ported directly from the original Parlant engine.
**Dart Class (`freezed`):**

```dart
import 'package:freezed_annotation/freezed_annotation.dart';
import 'condition.dart';
import 'action.dart';

part 'guideline.freezed.dart';
part 'guideline.g.dart';

@freezed
class Guideline with _$Guideline {
  const factory Guideline({
    required Condition condition,
    required Action action,
    @Default(0.5) double salience,
  }) = _Guideline;

  factory Guideline.fromJson(Map<String, dynamic> json) => _$GuidelineFromJson(json);
}
```

### 5\. Condition

**Purpose:** Defines the logical check for a `Guideline`.
**Dart Class (`freezed`):**

```dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'condition.freezed.dart';
part 'condition.g.dart';

@freezed
class Condition with _$Condition {
  const factory Condition({
    required String variable,
    required String operator,
    required dynamic value,
  }) = _Condition;

  factory Condition.fromJson(Map<String, dynamic> json) => _$ConditionFromJson(json);
}
```

### 6\. Action

**Purpose:** Defines the behavior to be executed by a `Guideline` as a type-safe union.
**Dart Class (`freezed`):**

```dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'action.freezed.dart';
part 'action.g.dart';

@freezed
class Action with _$Action {
  const factory Action.reply({ required String text }) = ReplyAction;
  const factory Action.runTool({ required String toolId, @Default({}) Map<String, dynamic> parameters }) = RunToolAction;
  
  factory Action.fromJson(Map<String, dynamic> json) => _$ActionFromJson(json);
}
```
