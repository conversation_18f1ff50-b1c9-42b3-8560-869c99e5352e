# Accessibility (a11y) Strategy

This section outlines the standards and practices we will follow to ensure the Parlant Journey Builder is accessible to users with disabilities, including those who use screen readers or require keyboard navigation.

## **1. Compliance Target**

* **Target:** Our goal is to meet the **Web Content Accessibility Guidelines (WCAG) 2.1 Level AA** standard. This is a globally recognized benchmark for creating accessible digital products.

## **2. Core Principles**

* **Semantics by Default:** Standard Flutter widgets provide rich accessibility support out of the box. Our primary strategy is to use these widgets correctly and only add custom accessibility information when necessary.
* **Manual Testing is Non-Negotiable:** While automated tools are helpful, they cannot replace manual testing. All features must be tested by a human using screen readers.

## **3. Implementation Guidelines**

* **Screen Reader Support (TalkBack/VoiceOver):**
    * All interactive elements without text labels (e.g., `IconButton`s) **must** be wrapped in a `Semantics` widget with a descriptive `label`.
    * `Semantics` widgets will also be used to group related controls into a single, understandable block for screen reader users.
    * **Example:** `Semantics(label: 'Add a new node to the canvas', child: IconButton(icon: Icon(Icons.add), onPressed: ...))`
* **Keyboard Navigation (Desktop):**
    * All interactive elements must be reachable and operable via keyboard.
    * A logical focus order will be maintained. Where Flutter's default order is insufficient, we will use `FocusNode` and `FocusTraversalGroup` to manage the sequence.
* **Tap Target Sizes:**
    * All buttons, checkboxes, and other interactive elements **must** have a minimum tap/click target size of **48x48 logical pixels** to be easily usable on touch devices.
* **Color Contrast:**
    * All text **must** meet the WCAG 2.1 AA contrast ratio of at least **4.5:1** for normal text and **3:1** for large text against its background.

## **4. Testing Strategy**

* **Automated Checks:** We will use the accessibility-related linting rules provided by the Very Good Core `analysis_options.yaml` to catch common issues automatically.
* **Manual Testing Protocol:** Before a feature's pull request can be merged, the developer **must** perform a manual accessibility check covering:
    1.  Navigating the new feature using only a keyboard (on desktop).
    2.  Navigating and operating the new feature using TalkBack on Android and VoiceOver on iOS.
* **Tooling:** Developers will use the **Flutter DevTools Accessibility Inspector** to debug and validate the semantics tree of the application.