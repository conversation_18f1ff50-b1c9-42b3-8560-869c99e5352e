# Error Handling Strategy

Our strategy is based on a simple principle: catch errors at the lowest possible level (the Data Layer), wrap them in a custom, typed `Failure` object, and pass this object up the layers. The UI will never see a raw technical exception; it will only deal with our well-defined `Failure` types.

## **1. Error Flow**

This diagram illustrates how an error from an external API is caught and propagated up to the UI.

```mermaid
sequenceDiagram
    participant UI as Presentation Layer (View)
    participant VM as ViewModel
    participant Repo as Data Layer (Repository)
    participant API as External API

    VM->>Repo: 1. Makes a data request (e.g., getCompletion())
    activate Repo
    Repo->>API: 2. Makes HTTP call
    
    API-->>Repo: 3. Throws an error (e.g., 401 Unauthorized)
    
    note over Repo: 4. Catches the raw DioError.
    note over Repo: 5. Maps it to a custom, typed 'Failure' object.
    
    Repo-->>VM: 6. Returns Result.failure(ApiFailure(message: 'Invalid API Key'))
    deactivate Repo
    
    VM->>VM: 7. Updates its state to an 'error' state, holding the Failure object.
    
    note over VM, UI: 8. The UI, which is listening to the ViewModel, rebuilds.
    
    UI->>User: 9. Displays a user-friendly error message: "Invalid API Key. Please check your settings."
```

## **2. Error Response Format**

To achieve this, we will use a `Result` type, which is a `freezed` union that can represent either a success or a failure. This forces developers to handle the error case, preventing unhandled exceptions.

```dart
import 'package.freezed_annotation/freezed_annotation.dart';

part 'result.freezed.dart';

@freezed
abstract class Result<T, E extends Exception> with _$Result<T, E> {
  const factory Result.success(T data) = Success<T, E>;
  // REFINEMENT: The Failure case now includes the stack trace.
  const factory Result.failure(E error, StackTrace stackTrace) = Failure<T, E>;
}

// REFINEMENT: A more detailed and useful Failure class.
class ApiFailure implements Exception {
  final String message;
  final int? statusCode; // e.g., 404, 500
  final String? errorCode;  // e.g., "invalid_api_key"

  ApiFailure({required this.message, this.statusCode, this.errorCode});
}
```

## **3. Frontend Error Handling**

The UI will use the `ViewModel`'s state to determine whether to show data or an error message. The `stacked` package makes this very clean.

```dart
// In a View, using ViewModelBuilder
ViewModelBuilder<MyViewModel>.reactive(
  viewModelBuilder: () => MyViewModel(),
  builder: (context, viewModel, child) {
    if (viewModel.hasError) {
      // Show a user-friendly error message
      return Center(child: Text(viewModel.error.message));
    }
    if (viewModel.isBusy) {
      return const Center(child: CircularProgressIndicator());
    }
    // Show the data
    return MyDataWidget(data: viewModel.data);
  },
)
```

## **4. Backend (Data Layer) Error Handling**

The repositories will now be responsible for capturing the stack trace and reporting unexpected errors to a central service before returning the `Failure` object.

```dart
// In a repository method
Future<Result<String, ApiFailure>> getCompletion() async {
  // A central service for reporting errors to Sentry
  final _errorReporter = locator<ErrorReporterService>();

  try {
    final response = await _dio.post('/completions', ...);
    return Result.success(response.data['choices'][0]['message']['content']);
  } on DioError catch (e, s) { // REFINEMENT: Capture stack trace `s`
    
    final failure = ApiFailure(
      message: 'The API call failed: ${e.message}',
      statusCode: e.response?.statusCode,
    );
    
    // REFINEMENT: Report unexpected (5xx) errors to Sentry
    if (failure.statusCode == null || (failure.statusCode! >= 500)) {
      _errorReporter.report(e, s);
    }
    
    return Result.failure(failure, s);
  }
}
```
