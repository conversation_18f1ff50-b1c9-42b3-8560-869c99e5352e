# Coding Standards

## **Critical Fullstack Rules**

1.  **Strictly Adhere to VGW Analysis:** The `analysis_options.yaml` file from Very Good CLI is the source of truth for code style.
2.  **Dependency Injection is Mandatory:** Services must **always** be accessed via the `stacked` service locator.
3.  **Respect Layer Boundaries:** Lower layers (e.g., `Domain`) **must never** import from higher layers (e.g., `Presentation`).
4.  **ViewModels Own Business Logic:** All UI logic and state manipulation must be in a `ViewModel`. `Views` must remain simple.
5.  **Embrace Immutability:** State must be updated by creating a *new* instance of a model using `.copyWith()`.

## **Naming Conventions**

| Element | Convention | Example |
| :--- | :--- | :--- |
| **Files** | `snake_case` | `journey_canvas_view.dart` |
| **Classes/Types** | `UpperCamelCase` | `JourneyViewModel` |
| **Variables/Methods** | `lowerCamelCase` | `runJourney` |
| **Views (Widgets)** | `UpperCamelCase` ending in `View` | `JourneyCanvasView` |
| **ViewModels** | `UpperCamelCase` ending in `ViewModel` | `JourneyCanvasViewModel` |
