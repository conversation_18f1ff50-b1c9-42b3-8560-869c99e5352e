# Components

## **Presentation Layer**

*(Responsible for displaying the UI and handling user input. Contains no business logic.)*

  * **ViewModels (`CanvasViewModel`, `PropertiesPanelViewModel`, `TestPanelViewModel`):**
      * **Refinement (Performance):** These ViewModels are responsible for managing **transient, high-frequency UI state**. For example, `CanvasViewModel` will handle the real-time pixel coordinates of a node *while it is being dragged*. It will only notify the central service of the **final, committed position** once the user releases the node. This prevents performance bottlenecks.

## **Application Layer**

*(A new layer that acts as a coordinator between the UI and the core domain logic.)*

  * **`JourneyOrchestrationService`:**
      * **Refinement (Communication):** To solve communication complexity, this service will act as a reactive hub. It will expose **`Streams`** of the application state (e.g., `Stream<Node?> get selectedNodeStream`, `Stream<Journey> get currentJourneyStream`). The ViewModels will subscribe to these streams to stay synchronized in a decoupled manner.

## **Domain Layer**

*(Responsible for pure, stateless business logic.)*

  * **`ParlantEngine`:** Pure, stateless engine containing the core ported logic.
  * **`GuidelineProcessingService`:** Sub-component for evaluating guidelines.

## **Data Layer**

*(Responsible for all data operations.)*

  * **`JourneyRepository`:** Handles persistence for `Journey` and `Tool` definitions.
  * **`LLMRepository`:** Public-facing component for LLM communication.
  * **`ToolRepository`:** Public-facing component for Tool execution.
  * **New Internal Component - `ApiClient`:** A **shared, private** client responsible for the low-level implementation of HTTP requests. Both `LLMRepository` and `ToolRepository` will use this internal `ApiClient` to promote code reuse.

## Final Component Diagram

```mermaid
graph TD
    subgraph Presentation Layer; direction TB; CV(CanvasViewModel); PPV(PropertiesPanelViewModel); TPV(TestPanelViewModel); end
    subgraph Application Layer; JOS(JourneyOrchestrationService); end
    subgraph Domain Layer; PE(ParlantEngine); end
    subgraph Data Layer; direction TB; JR(JourneyRepository); LR(LLMRepository); TR(ToolRepository); end
    CV --> JOS; PPV --> JOS; TPV --> JOS;
    JOS --> PE; JOS --> JR;
    PE --> LR; PE --> TR;
    subgraph External; direction TB; DB[(surrealdb_wasm)]; LLM_API[(LLM API)]; TOOL_API[(External Tool API)]; end
    JR --> DB; LR --> LLM_API; TR --> TOOL_API;
```
