# Deployment Architecture

This section outlines the strategy for building and distributing the application to the various platforms.

## **1. Deployment Strategy**

Our application is a self-contained client-side package. "Deployment" refers to the process of building the platform-specific artifacts and distributing them to users.

  * **Frontend Deployment:**
      * **Platform:** Apple App Store (for iOS/macOS), Google Play Store (for Android), and direct downloads via a website or GitHub Releases (for Desktop).
      * **Build Command:** `flutter build <platform>` (e.g., `flutter build appbundle`, `flutter build ipa`).
      * **Output Directory:** The standard `build/` directory in the project root.
  * **Backend Deployment:**
      * **Platform:** Not applicable. The "backend" logic (Parlant Engine) is a Dart library that is compiled and bundled directly into the frontend application artifact. There is no separate server to deploy.

## **2. CI/CD Pipeline (Refined)**

We will use GitHub Actions, but with a more detailed strategy for a full CI/CD process.

### **Secrets Management**

All sensitive information required for the pipeline (API keys, code signing passwords, certificates) will be stored as **GitHub Encrypted Secrets** and accessed securely within the workflow.

### **Versioning and Build Numbering**

The `version` in `pubspec.yaml` will be managed manually for major/minor/patch releases. The build number will be **automatically incremented** on every commit to the `main` branch using a script in the pipeline. This ensures every production build has a unique version.

### **Deployment Automation**

To automate the complex process of building, signing, and uploading to the app stores, we will use **Fastlane**. Our GitHub Actions workflow will call a Fastlane "lane" to handle the deployment.

**Revised `.github/workflows/main.yaml`:**

```yaml
name: CI/CD Pipeline
# ... (on push/pull_request triggers) ...
jobs:
  build_and_test:
    # ... (checkout, setup flutter, get dependencies, analyze, test) ...

  deploy_android:
    needs: build_and_test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      # ... checkout, setup flutter, setup ruby/bundler ...
      - name: Setup Fastlane
        run: bundle install
      - name: Deploy to Google Play Internal Track
        run: bundle exec fastlane android deploy_internal
        env:
          SIGNING_KEY_PASSWORD: ${{ secrets.ANDROID_SIGNING_KEY_PASSWORD }}
          # ... other secrets
  
  deploy_ios:
    # ... similar job for iOS deployment to TestFlight using fastlane ...
```

## **3. Build Flavors and Configurations (New)**

To manage different configurations for each environment, we will use **Flutter Flavors**. This allows us to compile different versions of the app from the same codebase. We will create three flavors:

  * **`dev`:** For local development (uses a `.env` file).
  * **`staging`:** For internal testing (points to test APIs, uses different analytics keys).
  * **`prod`:** The public release version.

Each flavor will have its own entry point (e.g., `main_staging.dart`) and can be compiled with a specific configuration.

## **4. Desktop Deployment Strategy (New)**

Desktop deployment will be handled in a separate CI/CD job.

  * **Builds:** The pipeline will generate signed binaries for macOS (`.dmg`) and Windows (`.exe`).
  * **Signing:** This will require platform-specific certificates (e.g., an Apple Developer ID certificate for macOS notarization) stored in GitHub Secrets.
  * **Distribution:** Initial desktop releases will be attached to **GitHub Releases** for direct download.

## **5. Environments**

| Environment | Frontend URL | Backend URL | Purpose |
| :--- | :--- | :--- | :--- |
| **Development** | `localhost` | N/A | Local development and testing on developer machines. |
| **Staging** | TestFlight / Google Play Internal Track | N/A | Pre-production testing for a limited group of users. |
| **Production** | Apple App Store / Google Play Store | N/A | The live, public version of the application. |
