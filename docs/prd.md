# Parlant Python to Flutter Port Product Requirements Document (PRD)

## Intro Project Analysis and Context

### Existing Project Overview

The existing Parlant project is a Python package designed for procedural text generation. Its core function is to assemble grammatically correct sentences by manipulating word objects that have defined attributes such as part of speech, type, and form. The documentation clarifies it is a sophisticated Agentic Behavior Modeling Engine for LLM agents, featuring capabilities like behavioral guidelines, tool use, and domain adaptation.

### Available Documentation Analysis

The following documentation has been provided and analyzed for this PRD:

* [x] Tech Stack Documentation (`brownfield-architecture.md`)
* [x] Source Tree/Architecture (`brownfield-architecture.md`)
* [x] API Documentation (`brownfield-architecture.md`)
* [x] External API Documentation (`brownfield-architecture.md`)
* [x] Technical Debt Documentation (`brownfield-architecture.md`)

### Enhancement Scope Definition

#### Enhancement Type

* [ ] New Feature Addition
* [ ] Major Feature Modification
* [ ] Integration with New Systems
* [ ] Performance/Scalability Improvements
* [ ] UI/UX Overhaul
* [x] Technology Stack Upgrade
* [ ] Bug Fix and Stability Improvements

#### Enhancement Description

This project's primary objective is to port the core logic and functionality of the Parlant Python package into a new, cross-platform application built with Flutter. This involves a complete rewrite of the engine from Python to Dart and the creation of a user interface for interaction.

#### Impact Assessment

* [ ] Minimal Impact (isolated additions)
* [ ] Moderate Impact (some existing code changes)
* [ ] Significant Impact (substantial existing code changes)
* [x] Major Impact (architectural changes required)

### Goals and Background Context

#### Goals

* Replicate the core procedural text generation functionality of the Parlant Python package in a Dart/Flutter environment.
* Develop a user-friendly interface that allows users to interact with the text generation engine on mobile and desktop platforms.
* Ensure the new Flutter application is maintainable, scalable, and provides a solid foundation for future feature enhancements.

#### Background Context

The current Parlant tool exists as a Python library, which limits its accessibility to users who are comfortable working within a Python development environment. By porting this powerful text generation engine to Flutter, we can create a standalone application that is easily distributable and usable by a much wider audience across various operating systems, including mobile. This enhancement unlocks the value of Parlant for non-technical users.

### Change Log

| Change | Date | Version | Description | Author |
| :--- | :--- | :--- | :--- | :--- |
| Created | 2025-09-03 | 1.0 | Initial draft of the PRD for the Python to Flutter port. | John (PM) |

## Requirements

### Functional

1.  **FR1:** The application shall provide a mechanism for users to define and manage behavioral guidelines for an LLM agent, including a condition and an action.
2.  **FR2:** The Flutter application must replicate the core engine logic to process these guidelines and interact with a configured LLM provider (e.g., OpenAI, Anthropic).
3.  **FR3:** The system shall support connecting external tools (APIs, data fetchers) to the agent, triggered by specific interaction events.
4.  **FR4:** Users must be able to adapt the agent to a specific domain by defining domain-specific terminology and responses.
5.  **FR5:** The application must include a user interface for creating and testing agents, similar to the web interface available for the Python server.
6.  **FR6:** The system shall provide an "explainability" feature that shows why a specific guideline was triggered and followed during an interaction.

### Non Functional

1.  **NFR1:** The application must be built using Dart and the Flutter framework, ensuring cross-platform compatibility on iOS, Android, and desktop (Windows, Mac, Linux).
2.  **NFR2:** The application's architecture should be modular to allow for the future addition of different LLM providers.
3.  **NFR3:** The user interface must be responsive and provide a seamless experience across all target platforms.
4.  **NFR4:** The application should handle LLM API interactions asynchronously to ensure the UI remains responsive during requests.

### Compatibility Requirements

1.  **CR1:** The logic of the Dart-based behavior model must be functionally equivalent to the Python implementation, producing the same outcomes given the same inputs and guidelines.
2.  **CR2:** The Flutter application must be able to consume the same format of API keys (e.g., OpenAI API key) as the original Python package.
3.  **CR3:** The core concepts and terminology (e.g., 'Guidelines', 'Journeys', 'Tools') from the original Parlant framework should be maintained in the new application for consistency.

## User Interface Enhancement Goals

### Integration with Existing UI

While we are creating a new Flutter application, the existing Parlant documentation mentions a React front-end widget. To ensure a consistent user experience for anyone familiar with the original tool, the new Flutter UI should take strong conceptual inspiration from this existing web interface. The core workflows of defining guidelines, managing tools, and testing agents should feel familiar.

### Modified/New Screens and Views

This project will require the creation of several new screens. The initial set of core screens will include:

* **Agent Configuration Screen:** A primary workspace where users can create and edit agents, define behavioral guidelines (conditions and actions), and manage domain adaptations.
* **Tool Management Screen:** An interface to add, configure, and remove external tools that the agent can use.
* **Interaction/Testing Panel:** A chat-like interface where users can interact with their configured agent to test its behavior and view the results of the "explainability" feature in real-time.
* **Settings Screen:** For managing LLM provider API keys and other application-level configurations.

### UI Consistency Requirements

The new Flutter application must maintain a high degree of conceptual consistency with the original Parlant framework. This includes using the same terminology for core concepts (e.g., "Guidelines", "Journeys", "Tools") and ensuring the logical flow of creating and testing an agent is preserved. The design should be clean, modern, and prioritize clarity to make the complex process of agent configuration as straightforward as possible.

## Technical Constraints and Integration Requirements

### Existing Technology Stack

While we are building a new Flutter app, the backend logic is being ported from an existing Python package. The new Flutter application's technology stack is explicitly defined in the project guide, with the following user-specified modifications.

* **Languages**: Dart (latest stable)
* **Frameworks**: Flutter (latest stable)
* **State Management**: `stacked`
* **Data Models**: `freezed`
* **Dependency Injection**: `get_it`
* **Networking**: `dio`
* **Infrastructure**: The application will be a client-side tool, running on mobile (iOS, Android) and desktop platforms.

### Integration Approach

* **Database Integration Strategy**: A local database using `surrealdb_wasm` will be used for persisting journey configurations on the user's device.
* **API Integration Strategy**: The application will primarily integrate with external LLM provider APIs (like OpenAI, Anthropic). A dedicated service layer will handle these integrations using the `dio` package.
* **Frontend Integration Strategy**: Not applicable, as this is a new, self-contained Flutter application.

### Code Organization and Standards

* **Project Foundation**: The project will be generated using the **Very Good CLI `flutter_package` template**. This establishes the baseline for the project structure, linting rules, and CI/CD setup.
* **File Structure Approach**: The project must follow a feature-first, modular structure with distinct layers: Data (repositories, data sources), Domain (use cases, entities), and Presentation (UI, ViewModels).
* **Naming Conventions**: The project will adhere to the conventions established by the `stacked` architecture.
* **Coding Standards**: All code will adhere to the high standards set by the Very Good Core analysis options, enforced by the linter configured in `analysis_options.yaml`.
* **Documentation Standards**: All public classes and methods must include Dart doc comments.

### Deployment and Operations

* **Build Process Integration**: Standard Flutter build tooling, as configured by the Very Good CLI template, will be used to generate release artifacts.
* **Deployment Strategy**: The application will be deployed through standard platform app stores (Apple App Store, Google Play Store) and as downloadable binaries for desktop.
* **Monitoring and Logging**: A lightweight logging solution like `logger` will be implemented for debugging and issue diagnosis.

### Risk Assessment and Mitigation

* **Technical Risks**: The primary risk is the complexity of creating a dynamic, canvas-based UI in Flutter. Mitigation involves using established packages for the canvas and thoroughly prototyping this core feature first.
* **Integration Risks**: Ensuring compatibility with various LLM provider APIs presents a risk. This will be mitigated by creating an abstraction layer (Adapter pattern) for the API services.

## Epic and Story Structure

### Epic Approach

**Epic Structure Decision**: For this project, the work is structured as a **single, comprehensive epic**. The goal is to port the entire Parlant engine and build the Journey Builder application as one cohesive unit of work. This approach is suitable because all the required features are tightly interconnected and deliver their full value together upon completion.

## Epic 1: Parlant Engine Port and Flutter Journey Builder App

**Epic Goal**: To faithfully port the core logic of the Python-based Parlant Agentic Behavior Modeling Engine to Dart. This epic will also deliver a new, user-friendly Flutter application that allows users to visually construct, configure, and test agentic journeys on cross-platform devices.

**Integration Requirements**:
* The application must integrate with external LLM Provider APIs (e.g., OpenAI, Anthropic).
* The application will use `surrealdb_wasm` for local on-device storage of user-created journeys.

---

### **Story 1.1: Foundation & End-to-End "Hello World" Slice**

**As a** developer,
**I want** to set up the project using the Very Good CLI template and implement a minimal end-to-end data flow,
**so that** we can validate the core architectural choices (`stacked`, `get_it`, UI-to-engine communication) from the very beginning.

**Acceptance Criteria**:
1.  A new Flutter package is created using the `very_good_cli`'s `flutter_package` template.
2.  The `stacked` architecture, `get_it` for service location, and other core dependencies are added and configured.
3.  A placeholder Dart engine service can receive a value, apply a hardcoded transformation (e.g., append "-processed"), and return the result.
4.  A basic UI view is created that contains a button and a text display.
5.  When the button is pressed, the UI calls the placeholder engine service and displays the transformed result in the text display, proving the end-to-end connection works.

### **Story 1.2: Port Core Engine and Guideline Logic**

**As a** developer,
**I want** to port the fundamental Parlant logic for processing guidelines from Python to Dart,
**so that** the core of the agent's decision-making is functionally equivalent to the original.

**Acceptance Criteria**:
1.  Dart classes for `Guideline`, `Condition`, and `Action` are created using `freezed`.
2.  The core engine service is implemented to accept an input and a list of `Guideline` objects.
3.  The engine correctly evaluates the conditions and returns the action from the first matching guideline.
4.  A suite of unit tests is created that validates the ported Dart logic against a set of known inputs and expected outputs from the Python version.

### **Story 1.3: Build Visual Journey Canvas & Persistence**

**As a** user,
**I want** a visual canvas where I can drag, drop, and connect nodes,
**so that** I can build the structure of my agent's journey.

**Acceptance Criteria**:
1.  A UI view containing an interactive canvas is implemented.
2.  A palette of available node types (e.g., Input, LLM, Tool, Output) is displayed.
3.  Users can drag nodes from the palette and drop them onto the canvas.
4.  Users can draw connections between nodes to define the sequence of the journey.
5.  The state of the canvas (nodes and their connections) is saved to the local `surrealdb_wasm` database.
6.  Saved journeys are reloaded onto the canvas when the application restarts.

### **Story 1.4: Implement Node Properties Panel**

**As a** user,
**I want** to configure the specific details and content of each node on the canvas,
**so that** I can define the precise behavior of my agent.

**Acceptance Criteria**:
1.  Tapping on a node on the canvas opens a dedicated properties panel.
2.  The properties panel displays configuration options relevant to the selected node type (e.g., a text field for an `LLM` node's prompt).
3.  Users can edit the properties, and the changes are reflected in the node's state.
4.  All configuration changes are persisted to the local database when modified.

### **Story 1.5: Integrate LLM Provider and Real-time Test Panel**

**As a** user,
**I want** to connect to a language model and test my journey in real-time,
**so that** I can immediately see how my agent behaves.

**Acceptance Criteria**:
1.  A settings screen is created where a user can securely input and save an LLM provider API key.
2.  The `LLM` node in the engine is updated to make a real API call using the saved key and its configured prompt.
3.  A "Test Panel" UI is implemented, allowing a user to provide an initial input for the journey.
4.  A "Run" button in the Test Panel executes the full journey defined on the canvas.
5.  The final result of the journey is displayed in the designated `Output` node on the canvas.

### **Story 1.6: Implement External Tool Use**

**As a** user,
**I want** to define and use external tools (APIs) within my journeys,
**so that** my agent can interact with outside systems to perform actions or retrieve information.

**Acceptance Criteria**:
1.  A UI is created for users to define a new `Tool`, including its name and API endpoint.
2.  The `Tool` node in a journey can be configured to use one of the defined tools.
3.  When the engine executes a `Tool` node, it makes the configured API call.
4.  The data returned from the API call is made available to subsequent nodes in the journey.

### **Story 1.7: Add Explainability Feature**

**As a** user,
**I want** to see the step-by-step logic my agent followed during a test run,
**so that** I can understand and debug its behavior.

**Acceptance Criteria**:
1.  The Test Panel is enhanced to include a log or a visual history of the last test run.
2.  After a journey is executed, this log clearly shows which nodes were activated in sequence.
3.  For each step, the log displays the input to that node and the output it produced.
4.  If a `Guideline` was triggered, the log specifies which one was matched.
