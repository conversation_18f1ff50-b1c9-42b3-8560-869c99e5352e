# Parlant Brownfield Enhancement Architecture

## Introduction

This document outlines the architectural approach for porting the `parlant` Python library to a new Dart/Flutter package. Its primary goal is to serve as the guiding architectural blueprint for AI-driven development, ensuring a seamless and accurate translation of functionality while adopting idiomatic Dart patterns.

**Relationship to Existing Architecture:**
This document uses the analysis of the existing Python library as its foundation. It defines how the Python components and patterns will be re-implemented or replaced to create a new, standalone Dart package.

### Existing Project Analysis

A thorough analysis of the source Python project was conducted. The key findings are:

  * **Current Project State**: The source is a standard Python library acting as a Façade over `nltk`, `spacy`, and `textblob` to simplify NLP tasks like parsing, sentiment analysis, and NER.
  * **Available Documentation**: The analysis produced a comprehensive document detailing the source's tech stack, file structure, and public API, which will serve as our primary reference.
  * **Identified Constraints**: The most significant constraint is the dependency on large, external language models that must be downloaded separately, a pattern that is not ideal for a mobile package.

### Change Log

| Change | Date | Version | Description | Author |
| :--- | :--- | :--- | :--- | :--- |
| Initial Draft | 2025-09-03 | 1.0 | Initial architecture based on analysis | Winston (Architect) |

## Enhancement Scope and Integration Strategy

### Enhancement Overview

  * **Enhancement Type**: Library Port and Modernization
  * **Scope**: A 1:1 functional port of the `parlant` Python library to a new, standalone Dart/Flutter package.
  * **Integration Impact**: This project involves creating a new package from scratch. The integration work is focused on composing new components from the Dart ecosystem, rather than modifying an existing codebase.

### Integration Approach

  * **Code Integration Strategy**: We will replicate the Façade pattern from the Python library. Distinct Dart classes will be created for `Parser`, `SentimentAnalyzer`, and `NERRecognizer`. These classes will act as wrappers, orchestrating calls to a curated set of specialized Dart packages to achieve the desired functionality.
  * **Database Integration**: N/A - This is a self-contained library with no direct database integration.
  * **API Integration**: The primary integration will be with the public APIs of our chosen Dart NLP libraries. Candidate packages for evaluation include:
      * For tokenization & parsing: (e.g., `package:nlp_primitives`, `package:porter_stemmer`)
      * For sentiment analysis: (e.g., `package:sentiment_analysis`)
      * For Named Entity Recognition: This is the most challenging area and may require a custom solution or a less mature package. Further investigation is required.
  * **UI Integration**: N/A - This is a logic library designed to be consumed by UI applications but has no UI itself.

### Compatibility Requirements

  * **Existing API Compatibility**: **CRITICAL**: The public API of the new Dart package (class names, method signatures, and the structure of return values) must be functionally identical to the Python `parlant` library's API to meet the 1:1 porting goal.
  * **Database Schema Compatibility**: N/A
  * **UI/UX Consistency**: N/A
  * **Performance Impact**: The ported Dart implementation must have acceptable performance on mid-range mobile devices. A performance budget will be defined and tested to ensure NLP operations do not negatively impact UI responsiveness or battery life.

## Tech Stack Alignment

### Existing Technology Stack (Source for Porting)

This table documents the current stack of the Python library. Its components serve as the functional requirements and behavioral baseline for our new Dart package.

| Category | Current Technology | Version | Usage in Enhancement | Notes |
| :--- | :--- | :--- | :--- | :--- |
| Language | Python | \>=3.6 | Source of logic to be ported | Core language of the original library |
| NLP Core | NLTK | 3.6.5 | Source of parsing logic | Provides foundational NLP tasks like tokenization |
| NLP Core | SpaCy | 3.2.1 | Source of NER/parsing logic | Used for advanced parsing and Named Entity Recognition |
| NLP Core | TextBlob | 0.17.1 | Source of sentiment logic | Provides simplified sentiment analysis |
| Testing | unittest | N/A | Behavioral reference for tests | Standard Python library for unit testing |

### New Technology Additions (Target Stack for Dart/Flutter)

This table defines the definitive technology stack for the new Dart/Flutter package. All development must adhere to these choices.

| Technology | Version | Purpose | Rationale | Integration Method |
| :--- | :--- | :--- | :--- | :--- |
| Dart | latest stable | Core language for the new package | Official language for Flutter development | Used for all new code |
| Flutter | latest stable | Core framework for the package | Required for creating Flutter packages | Used for project structure and platform APIs |
| test | latest stable | Unit & integration testing | Standard testing framework for Dart | Added as a `dev_dependency` in `pubspec.yaml` |
| lints | latest stable | Code linting and style enforcement | Official recommended linter rules | Added as a `dev_dependency` in `pubspec.yaml` |
| `nlp_primitives` | (for evaluation) | Tokenization and stemming | Candidate library to replace NLTK functionality | Added as a dependency in `pubspec.yaml` |
| `sentiment_analysis`| (for evaluation) | Sentiment analysis | Candidate library to replace TextBlob functionality | Added as a dependency in `pubspec.yaml` |

## Data Models and Schema Changes

### New Data Models

The following Dart classes will be created to serve as the strongly-typed data transfer objects for the public API.

#### `TaggedToken` Class

  * **Purpose**: To represent a single word (token) and its associated Part-of-Speech (POS) tag.

  * **Integration**: This class replaces the `(string, string)` tuple returned by the Python `parser.pos_tag` method.

    ```dart
    /// Represents a word and its part-of-speech tag.
    class TaggedToken {
      /// The word token.
      final String token;

      /// The part-of-speech tag (e.g., 'NN', 'VBZ').
      final String tag;

      TaggedToken({required this.token, required this.tag});
    }
    ```

#### `SentimentResult` Class

  * **Purpose**: To hold the results of a sentiment analysis operation.

  * **Integration**: This class replaces the dictionary-like object returned by the Python `sentiment.analyze` method.

    ```dart
    /// Holds the polarity and subjectivity scores of a text.
    class SentimentResult {
      /// A score from -1.0 (negative) to 1.0 (positive).
      final double polarity;

      /// A score from 0.0 (objective) to 1.0 (subjective).
      final double subjectivity;

      SentimentResult({required this.polarity, required this.subjectivity});
    }
    ```

#### `NamedEntity` Class

  * **Purpose**: To represent a recognized named entity and its category.

  * **Integration**: This class replaces the `(string, string)` tuple returned by the Python `ner.recognize` method.

    ```dart
    /// Represents a named entity found in a text.
    class NamedEntity {
      /// The text of the entity (e.g., "Google", "Kuala Lumpur").
      final String text;

      /// The entity's label (e.g., 'ORG', 'GPE').
      final String label;

      NamedEntity({required this.text, required this.label});
    }
    ```

### Schema Integration Strategy

  * **API Contract**: The public methods of the new Dart classes (`Parser`, `SentimentAnalyzer`, `NERRecognizer`) will be updated to return these strongly-typed classes instead of generic lists or maps. For example, the `pos_tag` method will have the signature `Future<List<TaggedToken>> pos_tag(String text)`.
  * **Exception Handling**: The custom Python exceptions will be ported to custom Dart exception classes to maintain a clear error handling contract for developers.
      * `class ParsingError implements Exception { ... }`
      * `class ModelNotFoundError implements Exception { ... }`

## Component Architecture

### New Components

#### `Public API Classes` (`Parser`, `SentimentAnalyzer`, `NERRecognizer`)

  * **Responsibility**: To provide the public-facing API for the library, matching the 1:1 functional contract of the original Python classes. They will orchestrate calls to the internal `AssetManager` and `NlpProvider`.
  * **Key Interfaces**: The public methods (`tokenize`, `analyze`, `recognize`, etc.) as defined in the "Data Models" section.
  * **Dependencies**: `AssetManager` (for classes requiring models), `NlpProvider` (for core logic), `utils`, `exceptions`.
  * **Technology Stack**: Dart.

#### `AssetManager`

  * **Responsibility**: To manage the entire lifecycle of remote NLP models: checking the local cache, downloading from a server if needed, saving to the device, and providing the model file path to the rest of the application.
  * **Key Interfaces**: A primary method like `Future<File> getModel({required String modelUrl, required String modelName})`.
  * **Dependencies**: `package:http` (or `dio`), `package:path_provider`, `package:sembast` (for metadata).
  * **Technology Stack**: Dart.

#### `NlpProvider` (Interface and Adapters)

  * **Responsibility**: To act as an abstraction layer (Adapter Pattern) between our public API classes and the third-party Dart NLP packages. A single interface will define methods like `performNer`, and concrete adapter classes will implement this interface for each specific `pub.dev` package we use.
  * **Key Interfaces**: An abstract class `NlpProvider` that defines the contract for all NLP operations.
  * **Dependencies**: The chosen `pub.dev` NLP packages (e.g., `nlp_primitives`).
  * **Technology Stack**: Dart.

#### `Utilities` and `Exceptions`

  * **Responsibility**: A direct port of the logic from the Python `utils.py` and `exceptions.py` modules for shared helper functions and custom error handling.
  * **Key Interfaces**: Public utility functions and custom `Exception` classes.
  * **Dependencies**: None.
  * **Technology Stack**: Dart.

### Component Interaction Diagram

This diagram shows how a call to a public method flows through the new component architecture.

```mermaid
sequenceDiagram
    actor Dev as Flutter App Developer
    participant API as NERRecognizer (Public API)
    participant AM as AssetManager
    participant NLP_P as NlpProvider (Interface)
    
    Dev->>API: recognize("Some text...")
    API->>AM: getModel(...)
    Note over AM: Check metadata cache
    alt Model not cached
        AM->>CDN: GET /model.zip
        CDN-->>AM: Model File
        AM->>Device: Save file & update metadata
    end
    AM-->>API: return modelFile
    API->>NLP_P: performNer(text, modelFile)
    Note over NLP_P: Call goes to concrete adapter
    NLP_P-->>API: return rawNerResult
    API->>API: Format result to List<NamedEntity>
    API-->>Dev: return Future<List<Named-Entity>>
```
