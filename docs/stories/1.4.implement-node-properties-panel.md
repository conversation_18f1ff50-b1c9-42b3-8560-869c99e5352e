# Story 1.4: Implement Node Properties Panel

## Status
Draft

## Story
**As a** user,
**I want** to configure the specific details and content of each node on the canvas,
**so that** I can define the precise behavior of my agent.

## Acceptance Criteria
1.  Tapping on a node on the canvas opens a dedicated properties panel.
2.  The properties panel displays configuration options relevant to the selected node type (e.g., a text field for an `LLM` node's prompt).
3.  Users can edit the properties, and the changes are reflected in the node's state.
4.  All configuration changes are persisted to the local database when modified.

## Tasks / Subtasks
- [ ] Task 1: Create Properties Panel View and ViewModel (AC: 1, 2)
    - [ ] Subtask 1.1: Create `lib/features/journey_builder/presentation/widgets/properties_panel.dart`.
    - [ ] Subtask 1.2: Create `lib/features/journey_builder/presentation/viewmodels/properties_panel_viewmodel.dart`.
    - [ ] Subtask 1.3: The `PropertiesPanelViewModel` should listen to the `JourneyOrchestrationService` for the currently selected node.
    - [ ] Subtask 1.4: The `PropertiesPanel` should dynamically render different form fields based on the type of the selected node.
- [ ] Task 2: Implement Node Selection (AC: 1)
    - [ ] Subtask 2.1: In `JourneyCanvasView`, add logic to detect taps on nodes.
    - [ ] Subtask 2.2: On tap, call a method on the `JourneyOrchestrationService` to set the selected node.
- [ ] Task 3: Implement Property Editing and Persistence (AC: 3, 4)
    - [ ] Subtask 3.1: In `PropertiesPanelViewModel`, when a form field is changed, update the node's state.
    - [ ] Subtask 3.2: Call the `JourneyRepository`'s `saveJourney` method to persist the changes.
- [ ] Task 4: Write Widget Tests (AC: 2, 3)
    - [ ] Subtask 4.1: Write widget tests for the `PropertiesPanel` to verify that the correct form fields are displayed for each node type.
    - [ ] Subtask 4.2: Write tests to ensure that user input correctly updates the ViewModel's state.

## Dev Notes
This story connects the visual representation of a node with its underlying data and configuration.

### File Locations
- **Properties Panel Widget**: `lib/features/journey_builder/presentation/widgets/properties_panel.dart`
- **ViewModel**: `lib/features/journey_builder/presentation/viewmodels/properties_panel_viewmodel.dart`

### State Management
- The `JourneyOrchestrationService` will act as the central hub for managing the currently selected node. This is crucial for decoupling the `JourneyCanvasView` from the `PropertiesPanel`. [Source: docs/architecture/components.md]
- The `PropertiesPanelViewModel` will subscribe to a `selectedNodeStream` from the `JourneyOrchestrationService` to know which node to display. [Source: docs/architecture/components.md]

### Dynamic UI
- The `PropertiesPanel` will use a `switch` statement or a similar pattern on the `selectedNode` type to render the appropriate UI controls. For example:
  - `LLMNode`: Show a `TextField` for the `prompt`.
  - `ToolNode`: Show a dropdown to select a `toolId`.

## Testing
- Focus on widget tests for the `PropertiesPanel`.
- Create mock `Node` objects of each type to pass to the panel and verify that the correct widgets are rendered.
- Test the interaction between the form fields and the ViewModel.

## Change Log
| Date | Version | Description | Author |
|---|---|---|---|
| 2025-09-05 | 1.0 | Initial draft | BMad/sm |
