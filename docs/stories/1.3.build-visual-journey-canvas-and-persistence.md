# Story 1.3: Build Visual Journey Canvas & Persistence

## Status
Draft

## Story
**As a** user,
**I want** a visual canvas where I can drag, drop, and connect nodes,
**so that** I can build the structure of my agent's journey.

## Acceptance Criteria
1.  A UI view containing an interactive canvas is implemented.
2.  A palette of available node types (e.g., Input, LLM, Tool, Output) is displayed.
3.  Users can drag nodes from the palette and drop them onto the canvas.
4.  Users can draw connections between nodes to define the sequence of the journey.
5.  The state of the canvas (nodes and their connections) is saved to the local `surrealdb_wasm` database.
6.  Saved journeys are reloaded onto the canvas when the application restarts.

## Tasks / Subtasks
- [ ] Task 1: Create Canvas View and ViewModel (AC: 1)
    - [ ] Subtask 1.1: Create `lib/features/journey_builder/presentation/views/journey_canvas_view.dart`.
    - [ ] Subtask 1.2: Create `lib/features/journey_builder/presentation/viewmodels/journey_canvas_viewmodel.dart`.
    - [ ] Subtask 1.3: The ViewModel should manage the list of nodes and connections.
- [ ] Task 2: Implement Node Palette and Drag-and-Drop (AC: 2, 3)
    - [ ] Subtask 2.1: Create a `NodePalette` widget that displays the available `NodeType`s.
    - [ ] Subtask 2.2: Implement drag-and-drop functionality from the palette to the canvas.
    - [ ] Subtask 2.3: Dropping a node on the canvas should add a new `Node` object to the `JourneyCanvasViewModel`'s state.
- [ ] Task 3: Implement Node Connection Logic (AC: 4)
    - [ ] Subtask 3.1: Implement a way for users to draw connections between nodes on the canvas (e.g., by dragging from a node's connection point).
    - [ ] Subtask 3.2: Add the new `Connection` object to the `JourneyCanvasViewModel`'s state.
- [ ] Task 4: Implement Persistence with SurrealDB (AC: 5, 6)
    - [ ] Subtask 4.1: Create a `JourneyRepository` in the data layer.
    - [ ] Subtask 4.2: The repository should have `saveJourney(Journey journey)` and `loadJourney()` methods.
    - [ ] Subtask 4.3: Implement the methods using the `surrealdb_wasm` package to store the `Journey` object.
    - [ ] Subtask 4.4: The `JourneyCanvasViewModel` should call `saveJourney` whenever the canvas state changes.
    - [ ] Subtask 4.5: The `JourneyCanvasViewModel` should call `loadJourney` on initialization.
- [ ] Task 5: Write Widget and Integration Tests (AC: 1, 3, 5, 6)
    - [ ] Subtask 5.1: Write widget tests for the `JourneyCanvasView` and `NodePalette`.
    - [ ] Subtask 5.2: Write integration tests to verify the entire flow of adding nodes, connecting them, and persisting the state.

## Dev Notes
This story introduces the core interactive element of the application. The canvas should be intuitive and responsive.

### File Locations
- **View/ViewModel**: `lib/features/journey_builder/presentation/`
- **Feature-Specific Widgets**: `lib/features/journey_builder/presentation/widgets/`
- **Repository**: `lib/features/journey_builder/data/repositories/`

### State Management
- The `JourneyCanvasViewModel` will be the source of truth for the canvas state.
- To avoid performance issues, the ViewModel should only notify the `JourneyOrchestrationService` of the final, committed state of the journey, not during intermediate actions like dragging a node. [Source: docs/architecture/components.md]

### Persistence
- The entire `Journey` object, including all its nodes and connections, should be serialized and stored as a single document in SurrealDB.
- The `surrealdb_wasm` package will be used for local, on-device storage. The team's familiarity with SurrealDB is a key factor. [Source: docs/architecture/tech-stack.md]

## Testing
- Widget tests are crucial for this story to ensure the canvas and palette render correctly.
- Integration tests should be written to cover the interaction between the UI, ViewModel, and the `JourneyRepository` to ensure persistence works as expected.
- Mock the `JourneyRepository` in widget tests to isolate the UI from the database.

## Change Log
| Date | Version | Description | Author |
|---|---|---|---|
| 2025-09-05 | 1.0 | Initial draft | BMad/sm |
