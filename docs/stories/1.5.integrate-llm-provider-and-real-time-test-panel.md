# Story 1.5: Integrate LLM Provider and Real-time Test Panel

## Status
Draft

## Story
**As a** user,
**I want** to connect to a language model and test my journey in real-time,
**so that** I can immediately see how my agent behaves.

## Acceptance Criteria
1.  A settings screen is created where a user can securely input and save an LLM provider API key.
2.  The `LLM` node in the engine is updated to make a real API call using the saved key and its configured prompt.
3.  A "Test Panel" UI is implemented, allowing a user to provide an initial input for the journey.
4.  A "Run" button in the Test Panel executes the full journey defined on the canvas.
5.  The final result of the journey is displayed in the designated `Output` node on the canvas.

## Tasks / Subtasks
- [ ] Task 1: Create Settings UI (AC: 1)
    - [ ] Subtask 1.1: Create a `SettingsView` and `SettingsViewModel`.
    - [ ] Subtask 1.2: Add a secure text field for the API key.
    - [ ] Subtask 1.3: Add a button to save the API key to a secure storage (e.g., `flutter_secure_storage`).
- [ ] Task 2: Create Test Panel UI (AC: 3, 4)
    - [ ] Subtask 2.1: Create a `TestPanel` widget and `TestPanelViewModel`.
    - [ ] Subtask 2.2: The widget should include a `TextField` for user input and a "Run" button.
    - [ ] Subtask 2.3: The "Run" button should trigger the `runJourney` method on the `TestPanelViewModel`.
- [ ] Task 3: Implement LLM Repository (AC: 2)
    - [ ] Subtask 3.1: Create `LLMRepository` in the data layer.
    - [ ] Subtask 3.2: The repository will use the `dio` client to make API calls to the LLM provider.
    - [ ] Subtask 3.3: It should fetch the saved API key from secure storage.
- [ ] Task 4: Update Parlant Engine (AC: 2, 5)
    - [ ] Subtask 4.1: Modify the `ParlantEngine` to call the `LLMRepository` when it encounters an `LLMNode`.
    - [ ] Subtask 4.2: The engine should pass the final result to the `JourneyOrchestrationService`.
- [ ] Task 5: Update Journey Orchestration (AC: 5)
    - [ ] Subtask 5.1: The `JourneyOrchestrationService` should update the state of the `Output` node with the final result from the engine.
- [ ] Task 6: Write Integration Tests (AC: 2, 4, 5)
    - [ ] Subtask 6.1: Write integration tests for the `LLMRepository` using a mock HTTP client.
    - [ ] Subtask 6.2: Write an E2E test using `patrol` that simulates the full user flow: setting the API key, building a simple journey, running it from the test panel, and verifying the output.

## Dev Notes
This story connects the visual journey builder to a real language model, completing the core end-to-end functionality.

### File Locations
- **Settings UI**: `lib/features/settings/presentation/`
- **Test Panel Widget**: `lib/features/journey_builder/presentation/widgets/test_panel.dart`
- **LLM Repository**: `lib/features/journey_builder/data/repositories/llm_repository.dart`

### API Integration
- The `dio` package will be used for all HTTP communication. An instance of `dio` should be registered as a singleton and injected into the `LLMRepository`. [Source: docs/architecture/frontend-architecture.md]
- The `LLMRepository` is responsible for mapping the raw API response from the provider into a clean, internal domain model. [Source: docs/architecture/core-workflows.md]

### Security
- The LLM API key is sensitive data and **must** be stored using a secure storage solution like `flutter_secure_storage`. It should never be stored in plain text.

## Testing
- The most critical test for this story is the end-to-end (`patrol`) test. This test will validate the entire application flow and is essential for ensuring quality. [Source: docs/architecture/testing-strategy.md]
- When testing the `LLMRepository`, mock the `dio` client to avoid making real network calls. This makes the tests faster and more reliable.

## Change Log
| Date | Version | Description | Author |
|---|---|---|---|
| 2025-09-05 | 1.0 | Initial draft | BMad/sm |
