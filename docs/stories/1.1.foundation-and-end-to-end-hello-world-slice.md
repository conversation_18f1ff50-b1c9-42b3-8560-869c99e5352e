# Story 1.1: Foundation & End-to-End "Hello World" Slice

## Status
Draft

## Story
**As a** developer,
**I want** to set up the project using the Very Good CLI template and implement a minimal end-to-end data flow,
**so that** we can validate the core architectural choices (`stacked`, `get_it`, UI-to-engine communication) from the very beginning.

## Acceptance Criteria
1.  The `stacked` architecture, `get_it` for service location, and other core dependencies are added and configured.
2.  A placeholder Dart engine service can receive a value, apply a hardcoded transformation (e.g., append "-processed"), and return the result.
3.  A basic UI view is created that contains a button and a text display.
4.  When the button is pressed, the UI calls the placeholder engine service and displays the transformed result in the text display, proving the end-to-end connection works.

## Tasks / Subtasks
- [ ] Task 1: Add Core Dependencies (AC: 1)
    - [ ] Subtask 1.1: Add `stacked`, `stacked_services`, `get_it`, `freezed`, `dio`, `logger` to `pubspec.yaml`.
    - [ ] Subtask 1.2: Add `build_runner`, `stacked_generator`, `freezed` to `dev_dependencies` in `pubspec.yaml`.
- [ ] Task 2: Configure Stacked Architecture (AC: 1)
    - [ ] Subtask 2.1: Create the `lib/app/app.dart` file.
    - [ ] Subtask 2.2: Define the initial `StackedApp` with a `MaterialRoute` for a placeholder view and register `NavigationService` and `DialogService`. [Source: docs/architecture/frontend-architecture.md]
    - [ ] Subtask 2.3: Run the `stacked_generator` to generate `app.locator.dart` and `app.router.dart`.
- [ ] Task 3: Implement Placeholder Engine (AC: 2)
    - [ ] Subtask 3.1: Create a placeholder service `lib/features/journey_builder/domain/services/placeholder_engine_service.dart`.
    - [ ] Subtask 3.2: The service should have a method `String processValue(String value)` that returns `value + "-processed"`.
    - [ ] Subtask 3.3: Register the service as a `LazySingleton` in `lib/app/app.dart`.
- [ ] Task 4: Create "Hello World" View & ViewModel (AC: 3, 4)
    - [ ] Subtask 4.1: Create `lib/features/journey_builder/presentation/views/hello_world_view.dart` using the `StackedView` template. [Source: docs/architecture/frontend-architecture.md]
    - [ ] Subtask 4.2: Create `lib/features/journey_builder/presentation/viewmodels/hello_world_viewmodel.dart`.
    - [ ] Subtask 4.3: The View should contain a `Text` widget to display a result and a `Button` to trigger the action.
    - [ ] Subtask 4.4: The ViewModel should have a `String result` property and a method `runProcess()`.
    - [ ] Subtask 4.5: The `runProcess()` method should call the `PlaceholderEngineService`, update the `result` property, and notify listeners.
- [ ] Task 5: Write Tests (AC: 1, 2, 4)
    - [ ] Subtask 5.1: Write a unit test for the `PlaceholderEngineService`.
    - [ ] Subtask 5.2: Write a widget test for the `HelloWorldView` to verify UI elements and interaction with the ViewModel.

## Dev Notes
This story establishes the foundational structure of the application. All code must adhere strictly to the patterns and locations defined in the architecture documents.

### File Locations
- **App Setup**: `lib/app/app.dart` [Source: docs/architecture/unified-project-structure.md]
- **Feature-Specific Code**: `lib/features/journey_builder/...` [Source: docs/architecture/unified-project-structure.md]
- **Placeholder Service**: `lib/features/journey_builder/domain/services/placeholder_engine_service.dart`
- **View/ViewModel**: `lib/features/journey_builder/presentation/views/hello_world_view.dart` & `.../viewmodels/hello_world_viewmodel.dart`

### Core Dependencies
- **State Management**: `stacked` [Source: docs/architecture/tech-stack.md]
- **Service Location**: `get_it` (via `stacked`) [Source: docs/architecture/tech-stack.md]
- **Data Models**: `freezed` [Source: docs/architecture/tech-stack.md]
- **HTTP**: `dio` [Source: docs/architecture/tech-stack.md]
- **Logging**: `logger` [Source: docs/architecture/tech-stack.md]

### Naming Conventions
- Files: `snake_case` (e.g., `hello_world_view.dart`)
- Classes: `UpperCamelCase` (e.g., `HelloWorldViewModel`)
- Views: Must end in `View` (e.g., `HelloWorldView`)
- ViewModels: Must end in `ViewModel` (e.g., `HelloWorldViewModel`)
[Source: docs/architecture/coding-standards.md]

### View Template
All new views must use the `StackedView` template provided in `docs/architecture/frontend-architecture.md`.

## Testing
- **Unit Tests**: All services and ViewModels must have unit tests. Mocks should be used for external dependencies. Tests for the domain layer go in `test/features/journey_builder/domain/`. [Source: docs/architecture/testing-strategy.md]
- **Widget Tests**: All views must have widget tests to verify rendering and user interaction. Tests for the presentation layer go in `test/features/journey_builder/presentation/`. [Source: docs/architecture/testing-strategy.md]
- **Mocking**: Use `mocktail` for creating mocks. [Source: docs/architecture/testing-strategy.md]

## Change Log
| Date | Version | Description | Author |
|---|---|---|---|
| 2025-09-05 | 1.0 | Initial draft | BMad/sm |
| 2025-09-05 | 1.1 | Removed project setup task as repo is already initialized. | BMad/sm |

