# Story 1.7: Add Explainability Feature

## Status
Draft

## Story
**As a** user,
**I want** to see the step-by-step logic my agent followed during a test run,
**so that** I can understand and debug its behavior.

## Acceptance Criteria
1.  The Test Panel is enhanced to include a log or a visual history of the last test run.
2.  After a journey is executed, this log clearly shows which nodes were activated in sequence.
3.  For each step, the log displays the input to that node and the output it produced.
4.  If a `Guideline` was triggered, the log specifies which one was matched.

## Tasks / Subtasks
- [ ] Task 1: Enhance Parlant Engine for Tracing (AC: 2, 3, 4)
    - [ ] Subtask 1.1: Modify the `ParlantEngine` to record each step of execution in an `ExecutionContext` object.
    - [ ] Subtask 1.2: The `ExecutionContext` should store a list of trace events, including node activations, inputs, outputs, and matched guidelines.
    - [ ] Subtask 1.3: The `execute` method should return the `ExecutionContext` along with the final result.
- [ ] Task 2: Update Orchestration and State (AC: 1)
    - [ ] Subtask 2.1: The `JourneyOrchestrationService` should receive the `ExecutionContext` from the engine.
    - [ ] Subtask 2.2: The service should expose the execution trace on a stream for the UI to consume.
- [ ] Task 3: Create Execution Log UI (AC: 1, 2, 3, 4)
    - [ ] Subtask 3.1: Create a new `ExecutionLog` widget.
    - [ ] Subtask 3.2: The widget should listen to the execution trace stream from the `JourneyOrchestrationService`.
    - [ ] Subtask 3.3: It should display the trace information in a clear, readable format (e.g., a scrolling list of log entries).
- [ ] Task 4: Write Widget and Integration Tests (AC: 1, 2, 3, 4)
    - [ ] Subtask 4.1: Write widget tests for the `ExecutionLog` widget to verify it correctly displays trace data.
    - [ ] Subtask 4.2: Write integration tests to ensure the end-to-end flow of running a journey and displaying the trace works correctly.

## Dev Notes
This feature is crucial for user trust and debuggability. The execution trace must be clear and easy to understand.

### File Locations
- **Execution Log Widget**: `lib/features/journey_builder/presentation/widgets/execution_log.dart`

### State Management
- The `JourneyOrchestrationService` is the key component for this story. It will take the detailed `ExecutionResult` from the `ParlantEngine` and expose it to the UI. [Source: docs/architecture/core-workflows.md]
- The `TestPanelViewModel` will be updated to listen for the new execution trace and pass it to the `ExecutionLog` widget.

### Data Flow
- The `ParlantEngine` must be enhanced to produce a rich `ExecutionResult` that contains not just the final output, but the full trace of the execution. This was anticipated in the core workflow design. [Source: docs/architecture/core-workflows.md]

## Testing
- Focus on integration tests for this story. It's important to verify that the trace generated by the engine is correctly passed through the service and displayed in the UI.
- In your widget tests for the `ExecutionLog`, create mock `ExecutionContext` objects with various trace events to ensure the UI handles all cases correctly.

## Change Log
| Date | Version | Description | Author |
|---|---|---|---|
| 2025-09-05 | 1.0 | Initial draft | BMad/sm |
