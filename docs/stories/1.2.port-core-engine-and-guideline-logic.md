# Story 1.2: Port Core Engine and Guideline Logic

## Status
Draft

## Story
**As a** developer,
**I want** to port the fundamental Parlant logic for processing guidelines from Python to Dart,
**so that** the core of the agent's decision-making is functionally equivalent to the original.

## Acceptance Criteria
1.  Dart classes for `Guideline`, `Condition`, and `Action` are created using `freezed`.
2.  The core engine service is implemented to accept an input and a list of `Guideline` objects.
3.  The engine correctly evaluates the conditions and returns the action from the first matching guideline.
4.  A suite of unit tests is created that validates the ported Dart logic against a set of known inputs and expected outputs from the Python version.

## Tasks / Subtasks
- [ ] Task 1: Create Domain Logic Models (AC: 1)
    - [ ] Subtask 1.1: Create `lib/features/journey_builder/domain/models/guideline.dart` using `freezed` as defined in `docs/architecture/data-models.md`.
    - [ ] Subtask 1.2: Create `lib/features/journey_builder/domain/models/condition.dart` using `freezed`.
    - [ ] Subtask 1.3: Create `lib/features/journey_builder/domain/models/action.dart` using `freezed`.
    - [ ] Subtask 1.4: Run the `build_runner` to generate the `.freezed.dart` and `.g.dart` files.
- [ ] Task 2: Implement Parlant Engine Service (AC: 2, 3)
    - [ ] Subtask 2.1: Create the `ParlantEngine` service in `lib/features/journey_builder/domain/services/parlant_engine.dart`.
    - [ ] Subtask 2.2: Implement the `execute` method that takes an input string and a list of `Guideline` objects.
    - [ ] Subtask 2.3: The `execute` method should iterate through the guidelines, evaluate the `Condition` of each, and return the `Action` of the first one that matches.
    - [ ] Subtask 2.4: Register the `ParlantEngine` as a `LazySingleton` in `lib/app/app.dart`.
- [ ] Task 3: Write Unit Tests (AC: 4)
    - [ ] Subtask 3.1: Create a test file `test/features/journey_builder/domain/services/parlant_engine_test.dart`.
    - [ ] Subtask 3.2: Write multiple tests to cover different conditions and actions, validating the ported logic against known Python version outputs. Refer to `docs/parlant-python-repo.txt` for the original implementation and `docs/reliability-analysis.md` for critical test scenarios.
    - [ ] Subtask 3.3: Use `mocktail` if any dependencies need to be mocked (though this service should be pure logic).

## Dev Notes
This story is focused on porting the core business logic from Python to Dart. It is critical that the Dart implementation is functionally identical to the original. The original Python implementation can be found in `docs/parlant-python-repo.txt` and key reliability scenarios to test against are in `docs/reliability-analysis.md`.

### File Locations
- **Domain Models**: `lib/features/journey_builder/domain/models/` [Source: docs/architecture/unified-project-structure.md]
- **Domain Service**: `lib/features/journey_builder/domain/services/parlant_engine.dart` [Source: docs/architecture/components.md]
- **Unit Tests**: `test/features/journey_builder/domain/services/` [Source: docs/architecture/testing-strategy.md]

### Data Models
- The `Guideline`, `Condition`, and `Action` classes must be implemented exactly as specified in `docs/architecture/data-models.md` using the `freezed` package. This ensures immutability and type safety.

### Core Logic
- The `ParlantEngine` should be a pure, stateless service. It should not have any dependencies on the UI or other services if possible, making it highly testable.
- The logic for evaluating conditions must be robust and handle different operators as defined in the original Python engine.

## Testing
- A comprehensive suite of unit tests is the primary deliverable for this story. The tests must provide high confidence that the ported logic is correct.
- Refer to the Python implementation in `docs/parlant-python-repo.txt` for test cases and expected outcomes.
- Consult `docs/reliability-analysis.md` for critical scenarios that must be covered by tests to ensure the ported logic is complete and correct.
- All tests must follow the structure defined in `docs/architecture/testing-strategy.md`.

## Change Log
| Date | Version | Description | Author |
|---|---|---|---|
| 2025-09-05 | 1.0 | Initial draft | BMad/sm |
| 2025-09-05 | 1.1 | Added references to Python repo and reliability analysis for verification. | BMad/sm |
