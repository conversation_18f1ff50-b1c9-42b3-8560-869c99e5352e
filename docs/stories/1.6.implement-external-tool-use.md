# Story 1.6: Implement External Tool Use

## Status
Draft

## Story
**As a** user,
**I want** to define and use external tools (APIs) within my journeys,
**so that** my agent can interact with outside systems to perform actions or retrieve information.

## Acceptance Criteria
1.  A UI is created for users to define a new `Tool`, including its name and API endpoint.
2.  The `Tool` node in a journey can be configured to use one of the defined tools.
3.  When the engine executes a `Tool` node, it makes the configured API call.
4.  The data returned from the API call is made available to subsequent nodes in the journey.

## Tasks / Subtasks
- [ ] Task 1: Create Tool Definition UI (AC: 1)
    - [ ] Subtask 1.1: Create a `ToolManagementView` and `ToolManagementViewModel`.
    - [ ] Subtask 1.2: The UI should allow users to create, view, and delete tool definitions (name and API endpoint).
    - [ ] Subtask 1.3: Tool definitions should be persisted using the `JourneyRepository`.
- [ ] Task 2: Update Properties Panel for Tool Node (AC: 2)
    - [ ] Subtask 2.1: Modify the `PropertiesPanel` to show a dropdown of available tools when a `ToolNode` is selected.
    - [ ] Subtask 2.2: The selected tool's ID should be saved to the `ToolNode`'s state.
- [ ] Task 3: Implement Tool Repository (AC: 3)
    - [ ] Subtask 3.1: Create `ToolRepository` in the data layer.
    - [ ] Subtask 3.2: The repository will use the `dio` client to make API calls to the endpoint specified in the tool definition.
- [ ] Task 4: Update Parlant Engine (AC: 3, 4)
    - [ ] Subtask 4.1: Modify the `ParlantEngine` to call the `ToolRepository` when it encounters a `ToolNode`.
    - [ ] Subtask 4.2: The engine should pass the data returned from the tool to the next node in the journey.
- [ ] Task 5: Write Integration Tests (AC: 1, 3, 4)
    - [ ] Subtask 5.1: Write integration tests for the `ToolRepository` using a mock HTTP client.
    - [ ] Subtask 5.2: Write widget tests for the `ToolManagementView`.
    - [ ] Subtask 5.3: Write an E2E test that defines a tool, uses it in a journey, and verifies that the data from the tool is processed correctly.

## Dev Notes
This story expands the agent's capabilities beyond language processing to interact with the outside world via APIs.

### File Locations
- **Tool Management UI**: `lib/features/tool_management/presentation/`
- **Tool Repository**: `lib/features/journey_builder/data/repositories/tool_repository.dart`

### API Integration
- The `ToolRepository` will use the same shared `ApiClient` as the `LLMRepository` to make its HTTP requests. [Source: docs/architecture/components.md]
- The `ParlantEngine` is now responsible for orchestrating calls to both the `LLMRepository` and the `ToolRepository`.

### Data Flow
- The key challenge in this story is managing the data flow. The output of a `ToolNode` must be available as an input to the subsequent nodes. The `ExecutionContext` within the `ParlantEngine` should be updated to handle this. [Source: docs/architecture/core-workflows.md]

## Testing
- The E2E test is critical for this story to ensure the entire tool definition and execution pipeline works correctly.
- Use a mock API server (like `mock_web_server`) in your integration tests for the `ToolRepository` to simulate various API responses.

## Change Log
| Date | Version | Description | Author |
|---|---|---|---|
| 2025-09-05 | 1.0 | Initial draft | BMad/sm |
