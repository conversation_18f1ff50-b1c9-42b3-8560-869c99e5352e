# Parlant Journey Builder Fullstack Architecture Document

## Introduction

This document outlines the complete fullstack architecture for the Parlant Journey Builder, including the client-side Dart engine (backend logic), the Flutter UI (frontend), and their integration. It serves as the single source of truth for AI-driven development, ensuring consistency across the entire technology stack.

### Starter Template or Existing Project

The project is a new greenfield application. The PRD specifies that the foundation will be created using a starter template.

**Decision**: The project will be generated using the **Very Good CLI `flutter_package` template**. This choice establishes a high-quality foundation for the project structure, dependency management, linting rules, and automated testing, which this architecture will build upon.

### Change Log

| Date | Version | Description | Author |
| :--- | :--- | :--- | :--- | :--- |
| 2025-09-04 | 1.0 | Initial creation of the architecture document. | <PERSON> (Architect) |

## High Level Architecture

### Technical Summary

This architecture outlines a modern, client-side Flutter application designed for cross-platform deployment. It uses a modular, three-layer architecture (Presentation, Domain, Data) to ensure a clean separation of concerns. The user interface and dependency management will be managed using the **MVVM pattern and service locator provided by the `stacked` package**. Data persistence will be handled locally on the device using `surrealdb_wasm`, and external communication with LLM providers will be managed through a dedicated, abstracted service layer. This approach directly supports the PRD goals of creating a maintainable, scalable, and user-friendly application on a modern, cross-platform technology stack.

### Platform and Infrastructure Choice

As this is a client-side application, the primary "platform" is the user's device (iOS, Android, Windows, macOS, Linux). The "infrastructure" for distribution will be the respective platform app stores and direct downloads for desktop.

* **Platform:** User device (Cross-Platform via Flutter)
* **Key Services:** The application will not rely on a dedicated backend infrastructure. Instead, it will directly consume external, third-party services:
    * LLM Provider APIs (e.g., OpenAI, Anthropic)
* **Deployment Host:** Not applicable (client-side).

### Repository Structure

* **Structure:** Single Package.
* **Rationale:** The project will be generated using the Very Good CLI `flutter_package` template. This provides a robust, standardized structure for a single, high-quality Flutter package/application. While not a multi-package monorepo, it enforces a clean organization of code, tests, and dependencies within one repository. The core application code will reside within the `lib/` directory, structured by feature and layer (Data, Domain, Presentation).

### Performance and Scalability Strategy

To address the potential performance limitations of a purely client-side engine, the architecture will incorporate a key design principle: **the engine's core processing logic will be abstracted**. This means the `Domain Layer` will be designed in such a way that its computational tasks can be executed by either a local implementation (running on the device) or a remote one (via an API call). This hybrid approach provides the best of both worlds:
* **Default:** Fast, offline-capable processing for standard tasks on the user's device.
* **Future-Proofing:** The ability to offload computationally intensive journeys or leverage more powerful server-side models in the future without requiring a full architectural rewrite.

### High Level Architecture Diagram

```mermaid
graph TD
    subgraph User Device
        subgraph Flutter Application
            A[Presentation Layer (UI - Stacked Views/ViewModels)]
            B[Domain Layer (Parlant Engine - UseCases)]
            C[Data Layer (Repositories)]
        end
        D[Local Database (surrealdb_wasm)]
    end

    E[External LLM APIs (OpenAI, etc.)]

    User -- Interacts with --> A
    A -- Calls --> B
    B -- Uses --> C
    C -- manages --> D
    C -- Fetches from --> E
```

### Architectural Patterns

  * **Modular 3-Layer Architecture:** As defined in the project guide, we will separate concerns into `Presentation`, `Domain`, and `Data` layers. This is fundamental to the project's organization and maintainability.
  * **Model-View-ViewModel (MVVM):** The chosen state management package, `stacked`, is a powerful implementation of the MVVM pattern. **It also provides a complete solution for dependency injection via its service locator, which simplifies service management throughout the application.**
  * **Repository Pattern:** The Data layer will use repositories to abstract the data sources (`surrealdb_wasm` and external APIs), providing a clean API for the Domain layer to use without needing to know the implementation details of data fetching or storage.
  * **Adapter Pattern:** A dedicated service layer will use this pattern to create a common interface for interacting with different LLM provider APIs, allowing us to easily add new providers in the future without changing the core engine logic.

## Tech Stack

| Category | Technology | Version | Purpose | Rationale |
| :--- | :--- | :--- | :--- | :--- |
| **Frontend Language** | Dart | latest stable | Primary language for all code | Required by Flutter; provides type safety and high performance. |
| **Frontend Framework**| Flutter | latest stable | UI toolkit and application framework | Chosen for its cross-platform capabilities and rich widget library. |
| **State Management** | stacked | latest stable | State management and dependency injection | **High team familiarity, which reduces risk and accelerates development.** |
| **Data Models** | freezed | latest stable | Code generation for immutable classes | Defined in the project guide; ensures robust, testable data models. |
| **Backend Language** | Dart | latest stable | Language for the ported Parlant engine | Unifies the tech stack, allowing code and model sharing between UI and logic. |
| **Local Database** | surrealdb\_wasm| latest stable | Local, on-device data persistence | **High team familiarity with SurrealDB. This choice mitigates adoption risk.**|
| **API Integration** | dio | latest stable | HTTP client for external API calls | Defined in the project guide; a standard and powerful networking client for Dart. |
| **Unit/Widget Testing**| flutter\_test | SDK version | Unit & widget testing for UI and logic | The default testing framework provided by the Very Good CLI template. |
| **E2E Testing** | patrol | latest stable | End-to-end testing and automation | Recommended for its robustness and integration with the Flutter ecosystem. |
| **CI/CD** | GitHub Actions | N/A | Continuous Integration & Deployment | The default setup provided by the Very Good CLI `flutter_package` template. |
| **Logging** | logger | latest stable | In-app logging for debugging | A lightweight and flexible logging utility mentioned in the PRD. |
| **Error Reporting** | Sentry | latest stable | Crash and error reporting for releases | Recommended for monitoring application health in production. |

## Data Models

### **Application State Models**

*(These define the visual builder and the overall structure of the user's creation.)*

#### 1\. Journey

**Purpose:** This is the top-level model that represents a complete, runnable agent configuration. It contains all the nodes, connections, and global rules.
**Dart Class (`freezed`):**

```dart
import 'package:freezed_annotation/freezed_annotation.dart';
import 'node.dart';
import 'connection.dart';
import 'guideline.dart';

part 'journey.freezed.dart';
part 'journey.g.dart';

@freezed
class Journey with _$Journey {
  const factory Journey({
    required String id,
    required String name,
    required List<Node> nodes,
    required List<Connection> connections,
    List<Guideline>? globalGuidelines,
  }) = _Journey;

  factory Journey.fromJson(Map<String, dynamic> json) => _$JourneyFromJson(json);
}
```

#### 2\. Node

**Purpose:** Represents a single step or component on the visual canvas. This is a union type (sealed class) to handle different kinds of nodes.
**Dart Class (`freezed`):**

```dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'node.freezed.dart';
part 'node.g.dart';

@freezed
class Node with _$Node {
  const factory Node.input({ required String id, required Offset position, @Default('Start') String text }) = InputNode;
  const factory Node.llm({ required String id, required Offset position, required String prompt }) = LLMNode;
  const factory Node.tool({ required String id, required Offset position, required String toolId }) = ToolNode;
  const factory Node.output({ required String id, required Offset position, @Default('End') String text }) = OutputNode;

  factory Node.fromJson(Map<String, dynamic> json) => _$NodeFromJson(json);
}
```

#### 3\. Connection

**Purpose:** Represents the directional link between two nodes on the canvas, defining the flow of execution.
**Dart Class (`freezed`):**

```dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'connection.freezed.dart';
part 'connection.g.dart';

@freezed
class Connection with _$Connection {
  const factory Connection({
    required String id,
    required String sourceNodeId,
    required String targetNodeId,
  }) = _Connection;

  factory Connection.fromJson(Map<String, dynamic> json) => _$ConnectionFromJson(json);
}
```

-----

### **Domain Logic Models**

*(These are the Dart ports of the core Python classes from `parlant.txt`. They will be contained within the "data" property of the Nodes above.)*

#### 4\. Guideline

**Purpose:** Represents a single rule for the agent to follow, ported directly from the original Parlant engine.
**Dart Class (`freezed`):**

```dart
import 'package:freezed_annotation/freezed_annotation.dart';
import 'condition.dart';
import 'action.dart';

part 'guideline.freezed.dart';
part 'guideline.g.dart';

@freezed
class Guideline with _$Guideline {
  const factory Guideline({
    required Condition condition,
    required Action action,
    @Default(0.5) double salience,
  }) = _Guideline;

  factory Guideline.fromJson(Map<String, dynamic> json) => _$GuidelineFromJson(json);
}
```

#### 5\. Condition

**Purpose:** Defines the logical check for a `Guideline`.
**Dart Class (`freezed`):**

```dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'condition.freezed.dart';
part 'condition.g.dart';

@freezed
class Condition with _$Condition {
  const factory Condition({
    required String variable,
    required String operator,
    required dynamic value,
  }) = _Condition;

  factory Condition.fromJson(Map<String, dynamic> json) => _$ConditionFromJson(json);
}
```

#### 6\. Action

**Purpose:** Defines the behavior to be executed by a `Guideline` as a type-safe union.
**Dart Class (`freezed`):**

```dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'action.freezed.dart';
part 'action.g.dart';

@freezed
class Action with _$Action {
  const factory Action.reply({ required String text }) = ReplyAction;
  const factory Action.runTool({ required String toolId, @Default({}) Map<String, dynamic> parameters }) = RunToolAction;
  
  factory Action.fromJson(Map<String, dynamic> json) => _$ActionFromJson(json);
}
```

## API Specification

This section defines the contracts for the external APIs we will consume, primarily the LLM providers.

```yaml
openapi: 3.0.0
info:
  title: "Standardized LLM Provider API"
  version: "1.0.0"
  description: "A standardized contract for chat completion services that the Parlant application will consume. This allows different providers (OpenAI, Anthropic, etc.) to be used via an adapter."
servers:
  - url: "[https://api.provider.com](https://api.provider.com)"
    description: "This is a generic base URL; the actual URL will be configured in the app's settings."

paths:
  /v1/chat/completions:
    post:
      summary: "Get a chat completion from the LLM."
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatCompletionRequest'
      responses:
        '200':
          description: "Successful response from the LLM."
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatCompletionResponse'
        '401':
          description: "Unauthorized - API key is missing or invalid."

components:
  schemas:
    Message:
      type: object
      properties:
        role:
          type: string
          enum: [system, user, assistant]
        content:
          type: string

    ChatCompletionRequest:
      type: object
      properties:
        model:
          type: string
          example: "gpt-4"
        messages:
          type: array
          items:
            $ref: '#/components/schemas/Message'
    
    ChatCompletionResponse:
      type: object
      properties:
        id:
          type: string
        object:
          type: string
        created:
          type: integer
        model:
          type: string
        choices:
          type: array
          items:
            type: object
            properties:
              index:
                type: integer
              message:
                $ref: '#/components/schemas/Message'

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      description: "API Key provided by the LLM service."
```

## Components

### **Presentation Layer**

*(Responsible for displaying the UI and handling user input. Contains no business logic.)*

  * **ViewModels (`CanvasViewModel`, `PropertiesPanelViewModel`, `TestPanelViewModel`):**
      * **Refinement (Performance):** These ViewModels are responsible for managing **transient, high-frequency UI state**. For example, `CanvasViewModel` will handle the real-time pixel coordinates of a node *while it is being dragged*. It will only notify the central service of the **final, committed position** once the user releases the node. This prevents performance bottlenecks.

### **Application Layer**

*(A new layer that acts as a coordinator between the UI and the core domain logic.)*

  * **`JourneyOrchestrationService`:**
      * **Refinement (Communication):** To solve communication complexity, this service will act as a reactive hub. It will expose **`Streams`** of the application state (e.g., `Stream<Node?> get selectedNodeStream`, `Stream<Journey> get currentJourneyStream`). The ViewModels will subscribe to these streams to stay synchronized in a decoupled manner.

### **Domain Layer**

*(Responsible for pure, stateless business logic.)*

  * **`ParlantEngine`:** Pure, stateless engine containing the core ported logic.
  * **`GuidelineProcessingService`:** Sub-component for evaluating guidelines.

### **Data Layer**

*(Responsible for all data operations.)*

  * **`JourneyRepository`:** Handles persistence for `Journey` and `Tool` definitions.
  * **`LLMRepository`:** Public-facing component for LLM communication.
  * **`ToolRepository`:** Public-facing component for Tool execution.
  * **New Internal Component - `ApiClient`:** A **shared, private** client responsible for the low-level implementation of HTTP requests. Both `LLMRepository` and `ToolRepository` will use this internal `ApiClient` to promote code reuse.

### Final Component Diagram

```mermaid
graph TD
    subgraph Presentation Layer; direction TB; CV(CanvasViewModel); PPV(PropertiesPanelViewModel); TPV(TestPanelViewModel); end
    subgraph Application Layer; JOS(JourneyOrchestrationService); end
    subgraph Domain Layer; PE(ParlantEngine); end
    subgraph Data Layer; direction TB; JR(JourneyRepository); LR(LLMRepository); TR(ToolRepository); end
    CV --> JOS; PPV --> JOS; TPV --> JOS;
    JOS --> PE; JOS --> JR;
    PE --> LR; PE --> TR;
    subgraph External; direction TB; DB[(surrealdb_wasm)]; LLM_API[(LLM API)]; TOOL_API[(External Tool API)]; end
    JR --> DB; LR --> LLM_API; TR --> TOOL_API;
```

## External APIs

### 1\. LLM Provider API

  * **Purpose:** To provide the core generative AI capabilities for the Parlant engine via its `LLMNode`.
  * **Authentication:** `Bearer Token`.
  * **Rate Limits:** Provider-specific. Our application must handle `429 Too Many Requests` errors gracefully.
  * **Key Endpoints Used:** `POST /v1/chat/completions`.

### 2\. User-Defined Tool APIs

  * **Purpose:** To allow the agent to interact with any external service the user configures via a `ToolNode`.
  * **Supported Protocols:** RESTful APIs that accept and return `application/json`.
  * **Authentication:** We will support No Authentication, API Key in a custom header, and Bearer Token.

## Core Workflows

This sequence diagram illustrates the end-to-end process when a user runs a journey, including the error handling path.

```mermaid
sequenceDiagram
    actor User; participant TPV as TestPanelView; participant TPVM as TestPanelViewModel; participant JOS as JourneyOrchestrationService; participant PE as ParlantEngine; participant LR as LLMRepository; participant EXT as ExternalLLMAPI;
    User->>TPV: 1. Clicks "Run"
    TPV->>TPVM: 2. Calls runJourney(input)
    TPVM->>JOS: 3. Calls runJourney(input)
    activate JOS; JOS->>PE: 4. Calls execute(journey, input)
    note over PE: Internally creates an 'ExecutionContext' to manage this single run.
    activate PE; PE->>LR: 5. Calls getCompletion(prompt)
    activate LR; LR->>EXT: 6. Makes async HTTP request
    alt Successful API Call
        EXT-->>LR: 7a. Returns raw API response (e.g., JSON)
        note over LR: Maps raw JSON to clean, internal 'LLMResult' domain model.
        LR-->>PE: 8a. Returns Success(LLMResult)
        note over PE: Engine processes result and logs step in 'ExecutionContext'.
        PE-->>JOS: 9a. Returns Success(ExecutionResult)
        note over JOS: 'ExecutionResult' contains both the final output AND the full execution trace for explainability.
        JOS->>JOS: 10a. Updates state with ExecutionResult
        deactivate PE
        note over JOS, TPVM: 11a. JOS emits new state on a Stream
        TPVM->>TPV: 12a. UI rebuilds to show result and trace log
        TPV->>User: 13a. Displays final output and explainability steps
    else API Call Fails
        EXT-->>LR: 7b. Returns HTTP Error
        note over LR: Maps HTTP error to a clean, internal 'ApiFailure' domain model.
        LR-->>PE: 8b. Returns Failure(ApiFailure)
        PE-->>JOS: 9b. Propagates Failure(ApiFailure)
        deactivate PE
        JOS->>JOS: 10b. Updates state with error
        note over JOS, TPVM: 11b. JOS emits error state on the Stream
        TPVM->>TPV: 12b. UI rebuilds to show error state
        TPV->>User: 13b. Displays user-friendly error message
    end
    deactivate JOS; deactivate LR;
```

## Database Schema

This schema defines a normalized, relational structure within `surrealdb_wasm`, with separate tables for our main entities connected via Record Links.

An excellent decision. The initial schema is simple, but as an architect, I see areas where we can make it much more powerful and maintainable for the future.

### **Critique of the Current Schema**

The primary critique of the current "single document" schema is its handling of **reusable items** and **data normalization**.

For example, if you wanted to use the same `Guideline` or `Tool` in ten different journeys, you would have to copy and paste its full definition into each of the ten `journey` documents. If you later needed to update that single `Guideline`, you would have to find and manually update it in all ten places, which is inefficient and prone to errors.

### **Refined Database Schema**

To solve this, I propose a more relational or "normalized" schema that takes better advantage of SurrealDB's multi-model capabilities. We will create separate tables for our main entities and use SurrealDB's powerful **Record Links** to connect them. This is a far more robust and scalable approach.

**1. `journey` Table**

  * **Purpose:** Stores the top-level information for a journey.
  * **Example Record:**
    ```json
    {
        "id": "journey:b8sa4k2p9",
        "name": "My First Agent Journey"
    }
    ```

**2. `node` Table**

  * **Purpose:** Stores the definition for every individual node. Each node is linked back to its parent journey.
  * **Example Record:**
    ```json
    {
        "id": "node:def",
        "journey": "journey:b8sa4k2p9", // Record Link to the parent journey
        "type": "llm",
        "position": { "x": 300, "y": 150 },
        "data": {
            "prompt": "Summarize the following text: {{input}}",
            // Node-specific guidelines could be linked here
            "guidelines": [ "guideline:abc", "guideline:def" ] 
        }
    }
    ```

**3. `connection` Table**

  * **Purpose:** A simple table to define the relationships between nodes. SurrealDB is also a graph database, so we could use graph edges, but a simple table is also very effective.
  * **Example Record:**
    ```json
    {
        "id": "connection:123",
        "journey": "journey:b8sa4k2p9",
        "from": "node:abc", // Record Link to the source node
        "to": "node:def"   // Record Link to the target node
    }
    ```

**4. `guideline` Table**

  * **Purpose:** Stores a central library of reusable guidelines.
  * **Example Record:**
    ```json
    {
        "id": "guideline:abc",
        "name": "Handle Negative Sentiment",
        "condition": { "variable": "input_sentiment", "operator": "equals", "value": "negative" },
        "action": { "type": "reply", "text": "I'm sorry to hear that." },
        "salience": 0.5
    }
    ```

**5. `tool_definition` Table**

  * **Purpose:** Stores a central library of reusable tool definitions. A `ToolNode` would simply contain a record link to a tool in this table.
  * **Example Record:**
    ```json
    {
        "id": "tool:xyz",
        "name": "Get Weather",
        "description": "Fetches the current weather for a city.",
        "apiUrl": "https://api.weather.com/forecast",
        "authMethod": "api_key_header",
        "authKey": "X-API-KEY"
    }
    ```

## Frontend Architecture

### **1. Component Architecture**

#### **Component Organization**

To better scale our UI, we will distinguish between widgets that are specific to a feature and widgets that are shared across the application.

```text
lib/
├── features/
│   └── journey_builder/
│       └── presentation/
│           ├── views/
│           │   └── journey_canvas_view.dart
│           ├── viewmodels/
│           │   └── journey_canvas_viewmodel.dart
│           └── widgets/  // Widgets ONLY for the journey_builder feature
│               ├── node_widget.dart
│               └── connection_painter.dart
└── ui/
    └── shared/         // Globally reusable widgets
        ├── app_colors.dart
        ├── custom_button.dart
        └── loading_indicator.dart
```

#### **View Template **

This template is more idiomatic for the `stacked` architecture and should be the standard for all new views.

```dart
import 'package:flutter/material.dart';
import 'package:stacked/stacked.dart';

import './my_cool_viewmodel.dart';

class MyCoolView extends StackedView<MyCoolViewModel> {
  const MyCoolView({super.key});

  @override
  Widget builder(
    BuildContext context,
    MyCoolViewModel viewModel,
    Widget? child,
  ) {
    return Scaffold(
      body: Center(
        child: Text(viewModel.title),
      ),
    );
  }

  @override
  MyCoolViewModel viewModelBuilder(
    BuildContext context,
  ) => MyCoolViewModel();
}
```

### **2. State Management Architecture**

As decided, we will use the `stacked` package.

#### **State Structure**

The core of the `stacked` architecture is the pairing of a `View` (a widget) with a `ViewModel`.

  * `..._view.dart`: A `StatelessWidget` that uses a `ViewModelBuilder` to construct the UI. It contains no business logic.
  * `..._viewmodel.dart`: A class that extends `ReactiveViewModel`. It holds the state, contains all business logic for the view, and interacts with services.

#### **State Management Patterns**

  * **Reactivity:** The `ViewModel` will notify the `View` to rebuild whenever its state changes.
  * **Service Locator:** All dependencies (like repositories or services) will be accessed from the `ViewModel` using the built-in `locator`.
  * **Business Logic:** All UI logic, state manipulation, and calls to services must reside in the `ViewModel`, not the `View`.

### **3. Routing Architecture**

#### **Route Organization (Refined)**

The `app.dart` file is also where we will register all our services for dependency injection. The example has been expanded to be more complete and realistic for our application.

```dart
// lib/app/app.dart
import 'package:stacked/stacked_annotations.dart';
import 'package:stacked_services/stacked_services.dart';
// ... import views and services ...

@StackedApp(
  routes: [
    MaterialRoute(page: JourneyCanvasView, initial: true),
    MaterialRoute(page: SettingsView),
  ],
  dependencies: [
    // Core Services
    LazySingleton(classType: NavigationService),
    LazySingleton(classType: DialogService),
    
    // HTTP Client
    LazySingleton(classType: ApiClient),
    
    // Data Layer Repositories
    LazySingleton(classType: JourneyRepository),
    LazySingleton(classType: LLMRepository),
    LazySingleton(classType: ToolRepository),
    
    // Application & Domain Layer Services
    LazySingleton(classType: JourneyOrchestrationService),
    LazySingleton(classType: ParlantEngine),
  ]
)
class App {}
```

### **4. Frontend Services Layer**

This defines how the frontend communicates with the Data Layer and external APIs.

#### **API Client Setup**

We will register our `dio` instance as a singleton service that can be injected into our repositories.

```dart
// In lib/app/app.dart dependencies list:
@LazySingleton()
Dio get dioService {
  final dio = Dio(BaseOptions(baseUrl: 'https://api.provider.com'));
  // Add interceptors for auth, logging, etc.
  return dio;
}
```

#### **Service Example**

A repository in the Data Layer would then use this injected `dio` instance to make API calls.

```dart
// In a repository class
class LLMRepository {
  final _dio = locator<Dio>();

  Future<String> getCompletion() async {
    // ... make a call using _dio instance
  }
}
```

## Backend Architecture

### **1. Service Architecture**

Our application uses a client-side service architecture composed of the Application, Domain, and Data layers we defined in the Components section.

  * **Application Layer (`JourneyOrchestrationService`):** Orchestrates the application's workflows. It is the bridge between the UI and the core engine.
  * **Domain Layer (`ParlantEngine`):** Contains the pure, stateless, ported business logic of Parlant. It is the "brain" of the application.
  * **Data Layer (`Repositories`):** Manages all data operations, abstracting the details of the local database and external API calls from the rest of the application.

This layered structure ensures a clean separation of concerns and high testability for our client-side logic.

### **2. Database Architecture**

#### **Schema Design**

As defined in our `Database Schema` section, we will use a normalized, relational structure within `surrealdb_wasm`, with separate tables for `journey`, `node`, `connection`, `guideline`, and `tool_definition`, all connected via Record Links. This prevents data duplication and provides a flexible foundation.

#### **Data Access Layer (Refined)**

The Repository Pattern remains, but the interface will be more comprehensive to support all the features our UI will need.

```dart
// A more complete interface for the JourneyRepository
abstract class IJourneyRepository {
  Future<Journey?> getJourney(String id);
  Future<List<JourneyHeader>> getAllJourneyHeaders(); // More efficient for lists
  Future<void> saveJourney(Journey journey);
  Future<void> deleteJourney(String id);
}

// JourneyHeader would be a lightweight class with just id and name for list views.
```

### **3. Authentication Architecture (Refined)**

To support multiple LLM providers and user-defined Tools, we need a more dynamic approach to authentication.

#### **New Component: `SecureStorageService`**

  * **Responsibility:** A dedicated service to securely manage a collection of API keys. It will store keys mapped to a unique identifier, like the API's base URL.
  * **Example Methods:** `saveKey(for: 'api.openai.com', key: '...')`, `getKey(for: 'api.openai.com')`.
  * **Technology:** It will use the `flutter_secure_storage` package as its underlying implementation.

#### **Authentication Flow**

1.  The user saves multiple API keys in the settings view, each associated with a service identifier (e.g., its base URL).
2.  The `SecureStorageService` saves these keys securely on the device.
3.  The `DynamicAuthInterceptor` (see below) uses this service to find the correct key for each outgoing request.

#### **Refined: `DynamicAuthInterceptor`**

The simple interceptor is replaced with a more intelligent one that can handle multiple services.

```dart
// A more robust Dio Interceptor for dynamic authentication
class DynamicAuthInterceptor extends Interceptor {
  final _secureStorageService; // Our new dedicated service

  DynamicAuthInterceptor(this._secureStorageService);

  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    // Identify the service being called by its base URL
    final serviceIdentifier = options.baseUrl;

    // Retrieve the specific key for that service
    final apiKey = await _secureStorageService.getKey(for: serviceIdentifier);

    if (apiKey != null) {
      // Logic to determine auth type (e.g., Bearer, X-API-Key)
      // This would be stored alongside the key.
      options.headers['Authorization'] = 'Bearer $apiKey';
    }

    return super.onRequest(options, handler);
  }
}
```

## Unified Project Structure

This section provides a definitive folder layout for our application. It combines the high-quality foundation from the Very Good CLI `flutter_package` template with the feature-first, Clean Architecture structure we have designed.

```plaintext
parlant_flutter/
├── .github/                  # CI/CD workflows from Very Good CLI
│   └── workflows/
│       └── main.yaml
├── lib/
│   ├── app/                    # App-level setup (routing, dependency injection)
│   │   ├── app.dart            # Stacked router and service definitions
│   │   └── app.locator.dart    # Generated by stacked_generator
│   │
│   ├── features/               # Feature-first directories
│   │   └── journey_builder/    # The main feature of our application
│   │       ├── application/  // Refined: New folder for application services
│   │       │   └── journey_orchestration_service.dart
│   │       │
│   │       ├── data/
│   │       │   ├── dtos/       // Refined: For data transfer objects if needed
│   │       │   └── repositories/
│   │       │
│   │       ├── domain/
│   │       │   ├── models/     // Refined: Core models moved to Domain layer
│   │       │   ├── repositories/ // Interfaces (abstract classes) for repositories
│   │       │   └── services/     // ParlantEngine
│   │       │
│   │       └── presentation/
│   │           ├── views/        # journey_canvas_view.dart
│   │           ├── viewmodels/   # canvas_viewmodel.dart, properties_viewmodel.dart
│   │           └── widgets/      # Feature-specific widgets (node_widget.dart)
│   │
│   ├── ui/                     # Shared, application-wide UI components
│   │   └── shared/
│   │       ├── custom_button.dart
│   │       └── loading_indicator.dart
│   │
│   └── parlant_flutter.dart    # Main package entry file
│
├── test/                     # All tests, mirroring the lib/ structure
│   └── ... (mirrors the lib/ structure)
├── analysis_options.yaml     # Linting rules (from Very Good Core)
├── pubspec.yaml              # Project dependencies
└── README.md
```

## Development Workflow

This section provides the practical, step-by-step instructions a developer will follow to set up the project, run tests, and manage their local environment. This is based on the standards provided by the Very Good CLI template.

### **1. Local Development Setup**

#### **Prerequisites**

Before working on this project, developers must have the following tools installed:

  * Flutter SDK (latest stable version)
  * Very Good CLI: `dart pub global activate very_good_cli`

#### **Initial Setup**

After cloning the repository for the first time, run the following command from the project root to fetch all dependencies:

```bash
flutter pub get
```

#### **Development Commands**

This project uses the standard tooling provided by Flutter and the Very Good CLI template.

```bash
# Run all tests and generate a coverage report
flutter test --coverage

# Check for outdated dependencies
flutter pub outdated

# Run the application on a connected device
# (e.g., iOS Simulator, Android Emulator, or Desktop)
flutter run

# Check code for analysis issues and linter warnings
flutter analyze
```

### **2. Environment Configuration**

We will manage environment variables (like API keys) using a `.env` file, which should not be committed to source control. The `flutter_dotenv` package will be used to load these variables at runtime.

#### **Required Environment Variables**

Developers will need to create a `.env` file in the project root by copying the `.env.example` file.

**`.env.example` file:**

```bash
# This file lists all required environment variables for the application.
# Copy this file to .env and fill in your own values.

# -- LLM Provider Configuration --
# The API key for your chosen LLM provider (e.g., OpenAI)
# This is retrieved by the LLMRepository.
OPENAI_API_KEY=YOUR_API_KEY_HERE

# The base URL for the LLM provider API.
# This allows for easy switching or proxying.
OPENAI_BASE_URL=[https://api.openai.com/v1](https://api.openai.com/v1)
```

## Deployment Architecture

This section outlines the strategy for building and distributing the application to the various platforms.

### **1. Deployment Strategy**

Our application is a self-contained client-side package. "Deployment" refers to the process of building the platform-specific artifacts and distributing them to users.

  * **Frontend Deployment:**
      * **Platform:** Apple App Store (for iOS/macOS), Google Play Store (for Android), and direct downloads via a website or GitHub Releases (for Desktop).
      * **Build Command:** `flutter build <platform>` (e.g., `flutter build appbundle`, `flutter build ipa`).
      * **Output Directory:** The standard `build/` directory in the project root.
  * **Backend Deployment:**
      * **Platform:** Not applicable. The "backend" logic (Parlant Engine) is a Dart library that is compiled and bundled directly into the frontend application artifact. There is no separate server to deploy.

### **2. CI/CD Pipeline (Refined)**

We will use GitHub Actions, but with a more detailed strategy for a full CI/CD process.

#### **Secrets Management**

All sensitive information required for the pipeline (API keys, code signing passwords, certificates) will be stored as **GitHub Encrypted Secrets** and accessed securely within the workflow.

#### **Versioning and Build Numbering**

The `version` in `pubspec.yaml` will be managed manually for major/minor/patch releases. The build number will be **automatically incremented** on every commit to the `main` branch using a script in the pipeline. This ensures every production build has a unique version.

#### **Deployment Automation**

To automate the complex process of building, signing, and uploading to the app stores, we will use **Fastlane**. Our GitHub Actions workflow will call a Fastlane "lane" to handle the deployment.

**Revised `.github/workflows/main.yaml`:**

```yaml
name: CI/CD Pipeline
# ... (on push/pull_request triggers) ...
jobs:
  build_and_test:
    # ... (checkout, setup flutter, get dependencies, analyze, test) ...

  deploy_android:
    needs: build_and_test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      # ... checkout, setup flutter, setup ruby/bundler ...
      - name: Setup Fastlane
        run: bundle install
      - name: Deploy to Google Play Internal Track
        run: bundle exec fastlane android deploy_internal
        env:
          SIGNING_KEY_PASSWORD: ${{ secrets.ANDROID_SIGNING_KEY_PASSWORD }}
          # ... other secrets
  
  deploy_ios:
    # ... similar job for iOS deployment to TestFlight using fastlane ...
```

### **3. Build Flavors and Configurations (New)**

To manage different configurations for each environment, we will use **Flutter Flavors**. This allows us to compile different versions of the app from the same codebase. We will create three flavors:

  * **`dev`:** For local development (uses a `.env` file).
  * **`staging`:** For internal testing (points to test APIs, uses different analytics keys).
  * **`prod`:** The public release version.

Each flavor will have its own entry point (e.g., `main_staging.dart`) and can be compiled with a specific configuration.

### **4. Desktop Deployment Strategy (New)**

Desktop deployment will be handled in a separate CI/CD job.

  * **Builds:** The pipeline will generate signed binaries for macOS (`.dmg`) and Windows (`.exe`).
  * **Signing:** This will require platform-specific certificates (e.g., an Apple Developer ID certificate for macOS notarization) stored in GitHub Secrets.
  * **Distribution:** Initial desktop releases will be attached to **GitHub Releases** for direct download.

### **5. Environments**

| Environment | Frontend URL | Backend URL | Purpose |
| :--- | :--- | :--- | :--- |
| **Development** | `localhost` | N/A | Local development and testing on developer machines. |
| **Staging** | TestFlight / Google Play Internal Track | N/A | Pre-production testing for a limited group of users. |
| **Production** | Apple App Store / Google Play Store | N/A | The live, public version of the application. |

## Security and Performance

### **1. Security Requirements**

  * **Frontend Security:**
      * **Secure Storage:** All sensitive data, most importantly API keys, **must** be stored on the device using the `flutter_secure_storage` package.
  * **Backend (Client-Side Engine) Security:**
      * **LLM Prompt Injection Mitigation:** The application will implement basic filtering for common injection patterns and display clear UI warnings to the user.
      * **Tool Execution Safeguards:** Timeouts, payload limits, and address validation **must** be implemented for the Tool feature to protect the user and the application.
  * **Authentication Security:**
      * **Token Storage:** All API keys and tokens will be managed exclusively through our `SecureStorageService`.

### **2. Performance Optimization**

  * **Frontend Performance:**
      * **Bundle Size Target:** We will use Flutter's build analysis tools to monitor application size.
      * **Loading Strategy:** We will employ lazy loading and display loading indicators during any asynchronous operation.
      * **Caching Strategy:** UI will be optimized with `const` widgets and short-lived in-memory data caching will be used.
  * **Backend (Client-Side Engine) Performance:**
      * **Response Time Target:** The primary goal is to **never block the UI thread**. All long-running operations must be performed asynchronously.
      * **Execution Governor:** The `ParlantEngine` **must** implement a governor with a maximum step limit and a total execution timeout to prevent runaway processes.
      * **Database Optimization:** Queries to the local `surrealdb_wasm` database will be optimized with appropriate indexing.

## Testing Strategy

### **1. Testing Pyramid**

Our testing philosophy will follow the standard testing pyramid. We will have a large base of fast and isolated unit tests, a healthy number of widget tests for our UI, and a small, focused set of end-to-end tests for our most critical user journeys.

```text
      / \
     / _ \      <-- End-to-End Tests (few, slow, integrated) [patrol]
    /_____\
   / _ _ _ \    <-- Widget Tests (many, medium speed) [flutter_test]
  /_________\
 / _ _ _ _ _ \  <-- Unit Tests (most, fast, isolated) [flutter_test]
/_____________\
```

### **2. Test Organization**

All tests will reside in the `test/` directory, which will mirror the structure of the `lib/` directory.

  * **Frontend Tests (Widget Tests):**
      * **Location:** `test/features/journey_builder/presentation/`
      * **Purpose:** To test individual Flutter widgets and views in isolation from services. We will verify that the UI renders correctly based on a given state and that it responds to user interactions.
  * **Backend Tests (Unit Tests):**
      * **Location:** `test/features/journey_builder/domain/` and `test/features/journey_builder/data/`
      * **Purpose:** To test our pure Dart logic. This includes the `ParlantEngine`, all services, and repositories. All external dependencies (like the database or HTTP client) will be mocked to ensure the tests are fast and isolated.
  * **Data Layer Testing Strategy:**
      * When unit testing our repositories (e.g., `JourneyRepository`, `LLMRepository`), we will mock their lowest-level dependencies. For example, when testing the `LLMRepository`, we will provide a mock version of the `dio` client to simulate API responses and failures. This ensures we are testing the repository's data mapping and logic in isolation.
  * **Visual Regression Testing (Golden Files):**
      * For our key shared UI components (`lib/ui/shared/`), we will create "golden file" tests. This practice involves generating a reference image of a widget and saving it. On subsequent test runs, a new image is generated and compared against the "golden" one to catch any unintended visual changes (e.g., color, padding, layout).
  * **E2E Tests:**
      * **Location:** `integration_test/` directory.
      * **Purpose:** The primary goal of our E2E test suite is to validate the complete, core user journey. The initial suite **must** cover the following scenario:
        1.  Launch the app.
        2.  Add at least two nodes to the canvas.
        3.  Connect the nodes.
        4.  Configure the nodes via the properties panel.
        5.  Execute the journey from the test panel.
        6.  Verify that the correct output is displayed in the output node.

### **3. Test Examples**

#### **Frontend Component Test (Widget Test)**

```dart
// test/features/journey_builder/presentation/widgets/node_widget_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:parlant_flutter/features/journey_builder/presentation/widgets/node_widget.dart';

void main() {
  testWidgets('NodeWidget displays the node text', (tester) async {
    await tester.pumpWidget(
      const MaterialApp(home: NodeWidget(text: 'My Node')),
    );

    expect(find.text('My Node'), findsOneWidget);
  });
}
```

#### **Backend Logic Test (Unit Test)**

```dart
// test/features/journey_builder/domain/services/parlant_engine_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart'; // A popular mocking library
import 'package:parlant_flutter/features/journey_builder/domain/services/parlant_engine.dart';

// Create a mock for the repository dependency
class MockLLMRepository extends Mock implements LLMRepository {}

void main() {
  test('ParlantEngine should return a simple reply from a guideline', () {
    // Arrange
    final mockRepo = MockLLMRepository();
    final engine = ParlantEngine(llmRepository: mockRepo);
    // ... setup guidelines and input ...

    // Act
    final result = engine.execute(journey, input);

    // Assert
    expect(result.output, 'Expected Reply');
  });
}
```

#### **Visual Regression Test (Golden File Example)**

```dart
// test/ui/shared/custom_button_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:parlant_flutter/ui/shared/custom_button.dart';

void main() {
  testWidgets('CustomButton matches golden file', (tester) async {
    await tester.pumpWidget(
      const MaterialApp(home: CustomButton(label: 'Submit')),
    );

    // This compares the rendered widget against a saved image file
    await expectLater(
      find.byType(CustomButton),
      matchesGoldenFile('goldens/custom_button.png'),
    );
  });
}
```

#### **E2E Test (`patrol`)**

```dart
// integration_test/app_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:patrol/patrol.dart';
import 'package:parlant_flutter/main.dart'; // Your app's entry point

void main() {
  patrolTest('User can create and run a simple journey',
    (PatrolIntegrationTester $) async {
      await $.pumpWidgetAndSettle(const MyApp());
      
      // Use Patrol's custom finders to interact with the app
      await $(#addNodeButton).tap();
      await $.native.enterText(find.byType(TextField), text: 'Hello, world!');
      await $(#runButton).tap();

      // Assert that the expected output appears
      expect($('Hello, world!-processed'), findsOneWidget);
    },
  );
}
```

## Coding Standards

### **Critical Fullstack Rules**

1.  **Strictly Adhere to VGW Analysis:** The `analysis_options.yaml` file from Very Good CLI is the source of truth for code style.
2.  **Dependency Injection is Mandatory:** Services must **always** be accessed via the `stacked` service locator.
3.  **Respect Layer Boundaries:** Lower layers (e.g., `Domain`) **must never** import from higher layers (e.g., `Presentation`).
4.  **ViewModels Own Business Logic:** All UI logic and state manipulation must be in a `ViewModel`. `Views` must remain simple.
5.  **Embrace Immutability:** State must be updated by creating a *new* instance of a model using `.copyWith()`.

### **Naming Conventions**

| Element | Convention | Example |
| :--- | :--- | :--- |
| **Files** | `snake_case` | `journey_canvas_view.dart` |
| **Classes/Types** | `UpperCamelCase` | `JourneyViewModel` |
| **Variables/Methods** | `lowerCamelCase` | `runJourney` |
| **Views (Widgets)** | `UpperCamelCase` ending in `View` | `JourneyCanvasView` |
| **ViewModels** | `UpperCamelCase` ending in `ViewModel` | `JourneyCanvasViewModel` |

## Error Handling Strategy

Our strategy is based on a simple principle: catch errors at the lowest possible level (the Data Layer), wrap them in a custom, typed `Failure` object, and pass this object up the layers. The UI will never see a raw technical exception; it will only deal with our well-defined `Failure` types.

### **1. Error Flow**

This diagram illustrates how an error from an external API is caught and propagated up to the UI.

```mermaid
sequenceDiagram
    participant UI as Presentation Layer (View)
    participant VM as ViewModel
    participant Repo as Data Layer (Repository)
    participant API as External API

    VM->>Repo: 1. Makes a data request (e.g., getCompletion())
    activate Repo
    Repo->>API: 2. Makes HTTP call
    
    API-->>Repo: 3. Throws an error (e.g., 401 Unauthorized)
    
    note over Repo: 4. Catches the raw DioError.
    note over Repo: 5. Maps it to a custom, typed 'Failure' object.
    
    Repo-->>VM: 6. Returns Result.failure(ApiFailure(message: 'Invalid API Key'))
    deactivate Repo
    
    VM->>VM: 7. Updates its state to an 'error' state, holding the Failure object.
    
    note over VM, UI: 8. The UI, which is listening to the ViewModel, rebuilds.
    
    UI->>User: 9. Displays a user-friendly error message: "Invalid API Key. Please check your settings."
```

### **2. Error Response Format**

To achieve this, we will use a `Result` type, which is a `freezed` union that can represent either a success or a failure. This forces developers to handle the error case, preventing unhandled exceptions.

```dart
import 'package.freezed_annotation/freezed_annotation.dart';

part 'result.freezed.dart';

@freezed
abstract class Result<T, E extends Exception> with _$Result<T, E> {
  const factory Result.success(T data) = Success<T, E>;
  // REFINEMENT: The Failure case now includes the stack trace.
  const factory Result.failure(E error, StackTrace stackTrace) = Failure<T, E>;
}

// REFINEMENT: A more detailed and useful Failure class.
class ApiFailure implements Exception {
  final String message;
  final int? statusCode; // e.g., 404, 500
  final String? errorCode;  // e.g., "invalid_api_key"

  ApiFailure({required this.message, this.statusCode, this.errorCode});
}
```

### **3. Frontend Error Handling**

The UI will use the `ViewModel`'s state to determine whether to show data or an error message. The `stacked` package makes this very clean.

```dart
// In a View, using ViewModelBuilder
ViewModelBuilder<MyViewModel>.reactive(
  viewModelBuilder: () => MyViewModel(),
  builder: (context, viewModel, child) {
    if (viewModel.hasError) {
      // Show a user-friendly error message
      return Center(child: Text(viewModel.error.message));
    }
    if (viewModel.isBusy) {
      return const Center(child: CircularProgressIndicator());
    }
    // Show the data
    return MyDataWidget(data: viewModel.data);
  },
)
```

### **4. Backend (Data Layer) Error Handling**

The repositories will now be responsible for capturing the stack trace and reporting unexpected errors to a central service before returning the `Failure` object.

```dart
// In a repository method
Future<Result<String, ApiFailure>> getCompletion() async {
  // A central service for reporting errors to Sentry
  final _errorReporter = locator<ErrorReporterService>();

  try {
    final response = await _dio.post('/completions', ...);
    return Result.success(response.data['choices'][0]['message']['content']);
  } on DioError catch (e, s) { // REFINEMENT: Capture stack trace `s`
    
    final failure = ApiFailure(
      message: 'The API call failed: ${e.message}',
      statusCode: e.response?.statusCode,
    );
    
    // REFINEMENT: Report unexpected (5xx) errors to Sentry
    if (failure.statusCode == null || (failure.statusCode! >= 500)) {
      _errorReporter.report(e, s);
    }
    
    return Result.failure(failure, s);
  }
}
```

## Monitoring and Observability (Refined)

### **1. User Privacy and Consent (New)**

The collection of any analytics or diagnostic data is a privilege, not a right.
* **Opt-In Consent:** The application **must** obtain explicit, opt-in consent from the user before enabling any analytics (Firebase) or error reporting (Sentry) services.
* **Anonymization:** All data sent to analytics services must be fully anonymized. We will not track any Personally Identifiable Information (PII).
* **Opt-Out:** The application settings will provide a clear option for the user to disable data collection at any time.

### **2. Monitoring Stack**

For our client-side application, the monitoring stack will be focused on error reporting, performance, and user analytics.

* **Frontend Monitoring (Analytics):** We will use **Firebase Analytics** (or Google Analytics for Firebase) to gather anonymized data on user engagement, feature adoption, and user flows.
* **Backend Monitoring (Client-Side):** The health of our client-side engine will be monitored via the tools below, not traditional server monitoring.
* **Error Tracking:** We will use **Sentry**, as defined in our tech stack. Our `ErrorReporterService` will send all unhandled exceptions and significant failures to Sentry for real-time alerts and debugging.
* **Performance Monitoring:** We will use **Sentry Performance Monitoring** to track application startup times, screen transition performance, and identify slow operations within the application.

### **3. Configuration Management (New)**

To ensure data from different environments is kept separate, we will use our **Flutter Flavors** system to manage monitoring configurations.
* The Sentry DSN (API key) and the Firebase configuration file (`google-services.json` / `GoogleService-Info.plist`) will be different for the `dev`, `staging`, and `prod` flavors.
* This guarantees that debug data is sent to a separate Sentry/Firebase project and never pollutes our production metrics.

### **4. Data Privacy and Scrubbing (New)**

To protect user privacy, we will enforce a strict policy on what data is allowed to leave the device.
* **Strict No-Log Rule:** Under no circumstances should any user-generated content be sent to external monitoring services. This includes, but is not limited to:
    * Text from prompts or nodes.
    * LLM API responses.
    * User-configured Tool details.
    * User-provided API keys.
* **Client-Side Scrubbing:** The Sentry client will be configured to automatically scrub all application state and view hierarchy data from error reports before they are sent. We will only send the exception and the relevant, non-sensitive parts of the stack trace.

### **5. Key Metrics**

We will track the following key metrics to measure the health and success of our application:

* **Frontend Metrics:**
    * **User Engagement:** Daily Active Users (DAU), user retention rates, average session length.
    * **Feature Adoption:** Frequency of key events, such as "Journey Created," "Journey Executed," and usage counts for each type of `Node`.
    * **Stability:** Crash-free user percentage (via Sentry).
    * **Performance:** App startup time, average screen load times (via Sentry).
* **Backend (Client-Side Engine) Metrics:**
    * **Error Rate:** The percentage of journey executions that result in a `Failure`. This will be tracked as a custom event in Sentry.
    * **Performance:** The average execution time for a journey. This can be tracked with Sentry Performance Monitoring.
    * **API Interaction:** Success/failure rates and average latency for calls made by the `LLMRepository` and `ToolRepository`. This is critical for monitoring our external dependencies and can be captured by Sentry.

## Accessibility (a11y) Strategy

This section outlines the standards and practices we will follow to ensure the Parlant Journey Builder is accessible to users with disabilities, including those who use screen readers or require keyboard navigation.

### **1. Compliance Target**

* **Target:** Our goal is to meet the **Web Content Accessibility Guidelines (WCAG) 2.1 Level AA** standard. This is a globally recognized benchmark for creating accessible digital products.

### **2. Core Principles**

* **Semantics by Default:** Standard Flutter widgets provide rich accessibility support out of the box. Our primary strategy is to use these widgets correctly and only add custom accessibility information when necessary.
* **Manual Testing is Non-Negotiable:** While automated tools are helpful, they cannot replace manual testing. All features must be tested by a human using screen readers.

### **3. Implementation Guidelines**

* **Screen Reader Support (TalkBack/VoiceOver):**
    * All interactive elements without text labels (e.g., `IconButton`s) **must** be wrapped in a `Semantics` widget with a descriptive `label`.
    * `Semantics` widgets will also be used to group related controls into a single, understandable block for screen reader users.
    * **Example:** `Semantics(label: 'Add a new node to the canvas', child: IconButton(icon: Icon(Icons.add), onPressed: ...))`
* **Keyboard Navigation (Desktop):**
    * All interactive elements must be reachable and operable via keyboard.
    * A logical focus order will be maintained. Where Flutter's default order is insufficient, we will use `FocusNode` and `FocusTraversalGroup` to manage the sequence.
* **Tap Target Sizes:**
    * All buttons, checkboxes, and other interactive elements **must** have a minimum tap/click target size of **48x48 logical pixels** to be easily usable on touch devices.
* **Color Contrast:**
    * All text **must** meet the WCAG 2.1 AA contrast ratio of at least **4.5:1** for normal text and **3:1** for large text against its background.

### **4. Testing Strategy**

* **Automated Checks:** We will use the accessibility-related linting rules provided by the Very Good Core `analysis_options.yaml` to catch common issues automatically.
* **Manual Testing Protocol:** Before a feature's pull request can be merged, the developer **must** perform a manual accessibility check covering:
    1.  Navigating the new feature using only a keyboard (on desktop).
    2.  Navigating and operating the new feature using TalkBack on Android and VoiceOver on iOS.
* **Tooling:** Developers will use the **Flutter DevTools Accessibility Inspector** to debug and validate the semantics tree of the application.