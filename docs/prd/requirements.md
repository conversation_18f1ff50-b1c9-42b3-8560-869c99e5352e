# Requirements

## Functional

1.  **FR1:** The application shall provide a mechanism for users to define and manage behavioral guidelines for an LLM agent, including a condition and an action.
2.  **FR2:** The Flutter application must replicate the core engine logic to process these guidelines and interact with a configured LLM provider (e.g., OpenAI, Anthropic).
3.  **FR3:** The system shall support connecting external tools (APIs, data fetchers) to the agent, triggered by specific interaction events.
4.  **FR4:** Users must be able to adapt the agent to a specific domain by defining domain-specific terminology and responses.
5.  **FR5:** The application must include a user interface for creating and testing agents, similar to the web interface available for the Python server.
6.  **FR6:** The system shall provide an "explainability" feature that shows why a specific guideline was triggered and followed during an interaction.

## Non Functional

1.  **NFR1:** The application must be built using Dart and the Flutter framework, ensuring cross-platform compatibility on iOS, Android, and desktop (Windows, Mac, Linux).
2.  **NFR2:** The application's architecture should be modular to allow for the future addition of different LLM providers.
3.  **NFR3:** The user interface must be responsive and provide a seamless experience across all target platforms.
4.  **NFR4:** The application should handle LLM API interactions asynchronously to ensure the UI remains responsive during requests.

## Compatibility Requirements

1.  **CR1:** The logic of the Dart-based behavior model must be functionally equivalent to the Python implementation, producing the same outcomes given the same inputs and guidelines.
2.  **CR2:** The Flutter application must be able to consume the same format of API keys (e.g., OpenAI API key) as the original Python package.
3.  **CR3:** The core concepts and terminology (e.g., 'Guidelines', 'Journeys', 'Tools') from the original Parlant framework should be maintained in the new application for consistency.
