# Epic 1: Parlant Engine Port and Flutter Journey Builder App

**Epic Goal**: To faithfully port the core logic of the Python-based Parlant Agentic Behavior Modeling Engine to Dart. This epic will also deliver a new, user-friendly Flutter application that allows users to visually construct, configure, and test agentic journeys on cross-platform devices.

**Integration Requirements**:
* The application must integrate with external LLM Provider APIs (e.g., OpenAI, Anthropic).
* The application will use `surrealdb_wasm` for local on-device storage of user-created journeys.

---

## **Story 1.1: Foundation & End-to-End "Hello World" Slice**

**As a** developer,
**I want** to set up the project using the Very Good CLI template and implement a minimal end-to-end data flow,
**so that** we can validate the core architectural choices (`stacked`, `get_it`, UI-to-engine communication) from the very beginning.

**Acceptance Criteria**:
1.  A new Flutter package is created using the `very_good_cli`'s `flutter_package` template.
2.  The `stacked` architecture, `get_it` for service location, and other core dependencies are added and configured.
3.  A placeholder Dart engine service can receive a value, apply a hardcoded transformation (e.g., append "-processed"), and return the result.
4.  A basic UI view is created that contains a button and a text display.
5.  When the button is pressed, the UI calls the placeholder engine service and displays the transformed result in the text display, proving the end-to-end connection works.

## **Story 1.2: Port Core Engine and Guideline Logic**

**As a** developer,
**I want** to port the fundamental Parlant logic for processing guidelines from Python to Dart,
**so that** the core of the agent's decision-making is functionally equivalent to the original.

**Acceptance Criteria**:
1.  Dart classes for `Guideline`, `Condition`, and `Action` are created using `freezed`.
2.  The core engine service is implemented to accept an input and a list of `Guideline` objects.
3.  The engine correctly evaluates the conditions and returns the action from the first matching guideline.
4.  A suite of unit tests is created that validates the ported Dart logic against a set of known inputs and expected outputs from the Python version.

## **Story 1.3: Build Visual Journey Canvas & Persistence**

**As a** user,
**I want** a visual canvas where I can drag, drop, and connect nodes,
**so that** I can build the structure of my agent's journey.

**Acceptance Criteria**:
1.  A UI view containing an interactive canvas is implemented.
2.  A palette of available node types (e.g., Input, LLM, Tool, Output) is displayed.
3.  Users can drag nodes from the palette and drop them onto the canvas.
4.  Users can draw connections between nodes to define the sequence of the journey.
5.  The state of the canvas (nodes and their connections) is saved to the local `surrealdb_wasm` database.
6.  Saved journeys are reloaded onto the canvas when the application restarts.

## **Story 1.4: Implement Node Properties Panel**

**As a** user,
**I want** to configure the specific details and content of each node on the canvas,
**so that** I can define the precise behavior of my agent.

**Acceptance Criteria**:
1.  Tapping on a node on the canvas opens a dedicated properties panel.
2.  The properties panel displays configuration options relevant to the selected node type (e.g., a text field for an `LLM` node's prompt).
3.  Users can edit the properties, and the changes are reflected in the node's state.
4.  All configuration changes are persisted to the local database when modified.

## **Story 1.5: Integrate LLM Provider and Real-time Test Panel**

**As a** user,
**I want** to connect to a language model and test my journey in real-time,
**so that** I can immediately see how my agent behaves.

**Acceptance Criteria**:
1.  A settings screen is created where a user can securely input and save an LLM provider API key.
2.  The `LLM` node in the engine is updated to make a real API call using the saved key and its configured prompt.
3.  A "Test Panel" UI is implemented, allowing a user to provide an initial input for the journey.
4.  A "Run" button in the Test Panel executes the full journey defined on the canvas.
5.  The final result of the journey is displayed in the designated `Output` node on the canvas.

## **Story 1.6: Implement External Tool Use**

**As a** user,
**I want** to define and use external tools (APIs) within my journeys,
**so that** my agent can interact with outside systems to perform actions or retrieve information.

**Acceptance Criteria**:
1.  A UI is created for users to define a new `Tool`, including its name and API endpoint.
2.  The `Tool` node in a journey can be configured to use one of the defined tools.
3.  When the engine executes a `Tool` node, it makes the configured API call.
4.  The data returned from the API call is made available to subsequent nodes in the journey.

## **Story 1.7: Add Explainability Feature**

**As a** user,
**I want** to see the step-by-step logic my agent followed during a test run,
**so that** I can understand and debug its behavior.

**Acceptance Criteria**:
1.  The Test Panel is enhanced to include a log or a visual history of the last test run.
2.  After a journey is executed, this log clearly shows which nodes were activated in sequence.
3.  For each step, the log displays the input to that node and the output it produced.
4.  If a `Guideline` was triggered, the log specifies which one was matched.
