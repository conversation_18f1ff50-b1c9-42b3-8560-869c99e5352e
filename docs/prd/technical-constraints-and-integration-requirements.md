# Technical Constraints and Integration Requirements

## Existing Technology Stack

While we are building a new Flutter app, the backend logic is being ported from an existing Python package. The new Flutter application's technology stack is explicitly defined in the project guide, with the following user-specified modifications.

* **Languages**: Dart (latest stable)
* **Frameworks**: Flutter (latest stable)
* **State Management**: `stacked`
* **Data Models**: `freezed`
* **Dependency Injection**: `get_it`
* **Networking**: `dio`
* **Infrastructure**: The application will be a client-side tool, running on mobile (iOS, Android) and desktop platforms.

## Integration Approach

* **Database Integration Strategy**: A local database using `surrealdb_wasm` will be used for persisting journey configurations on the user's device.
* **API Integration Strategy**: The application will primarily integrate with external LLM provider APIs (like OpenAI, Anthropic). A dedicated service layer will handle these integrations using the `dio` package.
* **Frontend Integration Strategy**: Not applicable, as this is a new, self-contained Flutter application.

## Code Organization and Standards

* **Project Foundation**: The project will be generated using the **Very Good CLI `flutter_package` template**. This establishes the baseline for the project structure, linting rules, and CI/CD setup.
* **File Structure Approach**: The project must follow a feature-first, modular structure with distinct layers: Data (repositories, data sources), Domain (use cases, entities), and Presentation (UI, ViewModels).
* **Naming Conventions**: The project will adhere to the conventions established by the `stacked` architecture.
* **Coding Standards**: All code will adhere to the high standards set by the Very Good Core analysis options, enforced by the linter configured in `analysis_options.yaml`.
* **Documentation Standards**: All public classes and methods must include Dart doc comments.

## Deployment and Operations

* **Build Process Integration**: Standard Flutter build tooling, as configured by the Very Good CLI template, will be used to generate release artifacts.
* **Deployment Strategy**: The application will be deployed through standard platform app stores (Apple App Store, Google Play Store) and as downloadable binaries for desktop.
* **Monitoring and Logging**: A lightweight logging solution like `logger` will be implemented for debugging and issue diagnosis.

## Risk Assessment and Mitigation

* **Technical Risks**: The primary risk is the complexity of creating a dynamic, canvas-based UI in Flutter. Mitigation involves using established packages for the canvas and thoroughly prototyping this core feature first.
* **Integration Risks**: Ensuring compatibility with various LLM provider APIs presents a risk. This will be mitigated by creating an abstraction layer (Adapter pattern) for the API services.
