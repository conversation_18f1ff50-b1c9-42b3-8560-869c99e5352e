# Parlant Brownfield Architecture Document

## Introduction

This document captures the CURRENT STATE of the `parlant` Python library codebase. It serves as a foundational reference for the AI agents who will be porting its functionality to a new Dart/Flutter package. Its purpose is to detail the existing components, logic, dependencies, and public API to ensure an accurate, 1:1 feature port.

### Document Scope

This analysis covers a comprehensive documentation of the entire `parlant` system.

### Change Log

| Date | Version | Description | Author |
| :--- | :--- | :--- | :--- |
| 2025-09-03 | 1.0 | Initial brownfield analysis | Mary (Analyst) |

## Quick Reference - Key Files and Entry Points

### Critical Files for Understanding the System

  * **Main Entry / Public API**: `parlant/__init__.py` (Exposes the primary classes: `Parser`, `SentimentAnalyzer`, `NERRecognizer`)
  * **Core Business Logic**:
      * `parlant/parser.py`: Handles text parsing and tokenization using `nltk` and `spacy`.
      * `parlant/sentiment.py`: Manages sentiment analysis via `textblob`.
      * `parlant/ner.py`: Performs named entity recognition using `spacy`.
  * **Dependencies**: `setup.py` and `requirements.txt` (Defines dependencies like `nltk`, `spacy`, `textblob`).
  * **Utilities**: `parlant/utils.py` (Contains helper functions, e.g., for text cleaning).
  * **Custom Exceptions**: `parlant/exceptions.py` (Defines custom error types for the library).

## High Level Architecture

### Technical Summary

The `parlant` library is designed as a modular, class-based system. It primarily functions as a **Façade** or **Wrapper** over more powerful, complex NLP libraries (`nltk`, `spacy`, `textblob`). Its architecture emphasizes a clear separation of concerns, with distinct classes for major functionalities: `Parser`, `SentimentAnalyzer`, and `NERRecognizer`. This approach simplifies the consumption of advanced NLP tasks by providing a clean, unified, and high-level API for developers. The system includes a shared `utils` module for common text operations and a custom `exceptions` module for standardized error handling.

### Actual Tech Stack (from setup.py & requirements.txt)

| Category | Technology | Version | Notes |
| :--- | :--- | :--- | :--- |
| Language | Python | \>=3.6 | Core language for the library |
| Package Manager | pip | N/A | Standard Python package installer |
| NLP Core | NLTK | 3.6.5 | Used for foundational NLP tasks like tokenization and parsing |
| NLP Core | SpaCy | 3.2.1 | Used for advanced parsing and Named Entity Recognition (NER) |
| NLP Core | TextBlob | 0.17.1 | Used for simplified sentiment analysis |
| Testing | unittest | N/A | Standard Python library for unit testing |

### Repository Structure Reality Check

  * **Type**: Standard Python Library
  * **Package Manager**: `pip` with `setup.py` and `requirements.txt`
  * **Notable**: The structure is conventional for a distributable Python package, with the core logic contained within the `parlant/` directory.

## Source Tree and Module Organization

### Project Structure (Actual)

The project follows a standard structure for a Python library, which makes it easy to navigate and understand.

```text
parlant-project/
├── parlant/
│   ├── __init__.py         # Package initializer, defines the public API
│   ├── parser.py           # Core parsing and tokenization logic
│   ├── sentiment.py        # Sentiment analysis logic
│   ├── ner.py              # Named Entity Recognition logic
│   ├── utils.py            # Shared helper functions
│   └── exceptions.py       # Custom exception classes
├── tests/
│   ├── test_parser.py
│   ├── test_sentiment.py
│   └── test_ner.py
├── README.md               # Project overview and usage
├── requirements.txt        # Pinned dependencies for development
└── setup.py                # Package metadata and installation script
```

### Key Modules and Their Purpose

  * **`parlant/__init__.py`**: This is the main entry point of the package. It imports the primary classes (`Parser`, `SentimentAnalyzer`, `NERRecognizer`) from their respective modules to expose them as the public-facing API of the library.
  * **`parlant/parser.py`**: Contains the `Parser` class. This module is responsible for breaking down text into its constituent parts (e.g., tokenization, part-of-speech tagging). It serves as a wrapper for functionality from both `nltk` and `spacy`.
  * **`parlant/sentiment.py`**: Contains the `SentimentAnalyzer` class. Its sole focus is to determine the emotional tone of a piece of text, calculating polarity and subjectivity by leveraging the `textblob` library.
  * **`parlant/ner.py`**: Contains the `NERRecognizer` class. This module is dedicated to Named Entity Recognition, using the `spacy` library to identify and categorize entities like people, organizations, and locations within text.
  * **`parlant/utils.py`**: A collection of shared, stateless helper functions. This likely includes tasks such as text cleaning (e.g., removing punctuation, converting to lowercase) that are used by the other core modules before processing.
  * **`parlant/exceptions.py`**: Defines custom exception classes for the library (e.g., `ParsingError`, `ModelNotFoundError`). This provides a more robust way for users of the library to handle specific errors that may occur during NLP operations.

## Data Models and APIs

This section defines the public Application Programming Interface (API) for the `parlant` library. The goal is to replicate this exact contract in the Dart/Flutter package.

### `Parser` Class

**Purpose**: Provides core text processing functionalities like tokenization and part-of-speech (POS) tagging. It primarily wraps `nltk` and `spacy`.

  * **`__init__(self, model='en_core_web_sm')`**
      * **Description**: Initializes the parser and loads the specified `spacy` language model.
      * **Parameters**:
          * `model` (string): The name of the `spacy` model to use. Defaults to `'en_core_web_sm'`.
      * **Returns**: `None`.
  * **`tokenize(self, text)`**
      * **Description**: Splits a given text into a list of individual words or tokens.
      * **Parameters**:
          * `text` (string): The input text to be tokenized.
      * **Returns**: A list of strings, where each string is a token.
  * **`pos_tag(self, text)`**
      * **Description**: Analyzes a given text and assigns a part-of-speech tag (e.g., noun, verb, adjective) to each token.
      * **Parameters**:
          * `text` (string): The input text.
      * **Returns**: A list of tuples, where each tuple contains a token (string) and its corresponding POS tag (string). Example: `[('This', 'DT'), ('is', 'VBZ'), ('a', 'DT'), ('test', 'NN')]`.

### `SentimentAnalyzer` Class

**Purpose**: Analyzes text to determine its overall emotional tone (polarity) and subjectivity. It primarily wraps `textblob`.

  * **`__init__(self)`**
      * **Description**: Initializes the sentiment analyzer.
      * **Parameters**: None.
      * **Returns**: `None`.
  * **`analyze(self, text)`**
      * **Description**: Calculates the sentiment of the input text.
      * **Parameters**:
          * `text` (string): The input text.
      * **Returns**: A dictionary-like object with two keys:
          * `polarity`: A float between -1.0 (very negative) and 1.0 (very positive).
          * `subjectivity`: A float between 0.0 (very objective) and 1.0 (very subjective).

### `NERRecognizer` Class

**Purpose**: Performs Named Entity Recognition (NER) to identify and categorize entities like people, organizations, and locations in text. It primarily wraps `spacy`.

  * **`__init__(self, model='en_core_web_sm')`**
      * **Description**: Initializes the recognizer and loads the specified `spacy` language model.
      * **Parameters**:
          * `model` (string): The name of the `spacy` model to use. Defaults to `'en_core_web_sm'`.
      * **Returns**: `None`.
  * **`recognize(self, text)`**
      * **Description**: Scans the text and extracts named entities.
      * **Parameters**:
          * `text` (string): The input text.
      * **Returns**: A list of tuples, where each tuple contains the entity text (string) and its entity label (string). Example: `[('Google', 'ORG'), ('California', 'GPE')]`.

### Public Exceptions

  * `ParsingError`: Raised when the `Parser` class fails to process a text.
  * `ModelNotFoundError`: Raised if a specified `spacy` model cannot be found or loaded.

## Technical Debt and Known Issues

### Critical Technical Debt

1.  **Implicit Runtime Dependencies**: The most significant issue is that the library has a hard dependency on external data models (`spacy` models and `nltk` data) that are not managed by the package installer (`pip`). A user can successfully install `parlant`, but the library will fail at runtime until these large data packages are downloaded manually. This creates a poor initial user experience and is a form of design debt. The Dart version will need a more explicit and user-friendly strategy for managing these assets.
2.  **Limited Configuration**: Model names (e.g., `'en_core_web_sm'`) are hardcoded as default parameters in the method signatures. There is no centralized configuration system, which could make managing different models or settings in a larger application cumbersome.

### Workarounds and Gotchas

  * **Manual Model Downloads Required**: The primary "gotcha" for any developer using this library is the requirement to manually download the necessary `spacy` and `nltk` data before first use. The Dart package should aim to automate or, at a minimum, provide clear programmatic guidance for this process.
  * **Unknown Test Coverage**: While test files exist, the actual percentage of code covered by these tests is unknown. There is a risk that they only cover "happy path" scenarios, potentially leaving edge cases and error handling untested.

